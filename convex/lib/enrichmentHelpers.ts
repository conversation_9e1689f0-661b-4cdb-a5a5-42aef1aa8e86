/**
 * Data enrichment helpers for standardized entity relationship resolution
 * 
 * These helpers provide consistent patterns for enriching database entities with
 * related data, reducing code duplication and improving query performance through
 * batch loading and caching patterns.
 */

import { Id } from "../_generated/dataModel";
import { 
  batchLoadUsers, 
  batchLoadTeams, 
  batchLoadSessions, 
  batchLoadEvents,
  batchLoadIdeas,
  batchLoadSparks,
  batchLoadQuickfires,
  batchLoadTeamMembers,
  batchLoadUserTeams,
  batchLoadEventStatistics,
  extractUniqueIds
} from "./batchHelpers";

/**
 * Standard enrichment interface for entities with common fields
 */
export interface EnrichedEntity {
  _id: Id<any>;
  _creationTime: number;
  createdAt: number;
  updatedAt?: number;
  
  // Common enriched fields
  userName?: string;
  userUsername?: string;
  teamName?: string;
  sessionName?: string;
  eventName?: string;
  sparkName?: string;
}

/**
 * Enrich users with their team information for a specific event
 * @param ctx - Database context
 * @param users - Array of user objects
 * @param eventId - Event ID to get team info for
 * @returns Array of users with team information
 */
export async function enrichUsersWithTeamInfo(
  ctx: any,
  users: any[],
  eventId: Id<"events">
): Promise<any[]> {
  if (users.length === 0) {
    return [];
  }

  const userIds = users.map((user: any) => user._id);
  const userTeams = await batchLoadUserTeams(ctx, eventId, userIds);

  return users.map((user: any) => {
    const team = userTeams.get(user._id);
    return {
      ...user,
      teamName: team?.name || null,
      teamId: team?._id || null
    };
  });
}

/**
 * Enrich teams with their member information
 * @param ctx - Database context
 * @param teams - Array of team objects
 * @param eventId - Event ID to get member info for
 * @returns Array of teams with member information
 */
export async function enrichTeamsWithMemberInfo(
  ctx: any,
  teams: any[],
  eventId: Id<"events">
): Promise<any[]> {
  if (teams.length === 0) {
    return [];
  }

  const teamIds = teams.map(team => team._id);
  const teamMembers = await batchLoadTeamMembers(ctx, eventId, teamIds);

  return teams.map(team => {
    const members = teamMembers.get(team._id) || [];
    return {
      ...team,
      members,
      memberCount: members.length,
      memberNames: members.map(member => member.name)
    };
  });
}

/**
 * Enrich sessions with their associated spark information
 * @param ctx - Database context
 * @param sessions - Array of session objects
 * @returns Array of sessions with spark information
 */
export async function enrichSessionsWithSparkInfo(
  ctx: any,
  sessions: any[]
): Promise<any[]> {
  if (sessions.length === 0) {
    return [];
  }

  // Extract spark IDs from sessions
  const sparkIds = sessions
    .filter(session => session.sparkId)
    .map(session => session.sparkId);

  if (sparkIds.length === 0) {
    return sessions.map(session => ({
      ...session,
      sparkName: null,
      sparkDescription: null
    }));
  }

  const sparks = await batchLoadSparks(ctx, sparkIds);

  return sessions.map(session => {
    const spark = session.sparkId ? sparks.get(session.sparkId) : null;
    return {
      ...session,
      sparkName: spark?.name || null,
      sparkDescription: spark?.description || null
    };
  });
}

/**
 * Enrich ideas with comprehensive related data
 * @param ctx - Database context
 * @param ideas - Array of idea objects
 * @returns Array of ideas with comprehensive enriched data
 */
export async function enrichIdeasWithComprehensiveData(
  ctx: any,
  ideas: any[]
): Promise<any[]> {
  if (ideas.length === 0) {
    return [];
  }

  // Extract unique IDs for batch loading
  const userIds = extractUniqueIds(ideas, "userId");
  const teamIds = extractUniqueIds(ideas, "teamId");
  const sessionIds = extractUniqueIds(ideas, "sessionId");
  const eventIds = extractUniqueIds(ideas, "eventId");

  // Batch load all related entities
  const [users, teams, sessions, events] = await Promise.all([
    batchLoadUsers(ctx, userIds),
    batchLoadTeams(ctx, teamIds),
    batchLoadSessions(ctx, sessionIds),
    batchLoadEvents(ctx, eventIds)
  ]);

  // Get vote counts for each idea
  const ideaVoteCounts = await Promise.all(
    ideas.map(async (idea) => {
      const votes = await ctx.db
        .query("votes")
        .withIndex("by_idea", (q: any) => q.eq("ideaId", idea._id))
        .collect();
      
      return {
        ideaId: idea._id,
        voteCount: votes.length,
        totalScore: votes.reduce((sum: number, vote: any) => sum + vote.score, 0),
        averageScore: votes.length > 0 ? votes.reduce((sum: number, vote: any) => sum + vote.score, 0) / votes.length : 0
      };
    })
  );

  const voteStatsMap = new Map(
    ideaVoteCounts.map(stats => [stats.ideaId.toString(), stats])
  );

  // Enrich ideas with all related data
  return ideas.map(idea => {
    const user = users.get(idea.userId);
    const team = teams.get(idea.teamId);
    const session = sessions.get(idea.sessionId);
    const event = events.get(idea.eventId);
    const voteStats = voteStatsMap.get(idea._id.toString());

    return {
      ...idea,
      userName: user?.name || "Unknown User",
      userUsername: user?.username || "Unknown",
      teamName: team?.name || "Unknown Team",
      sessionName: session?.name || "Unknown Session",
      eventName: event?.name || "Unknown Event",
      voteCount: voteStats?.voteCount || 0,
      totalScore: voteStats?.totalScore || 0,
      averageScore: voteStats?.averageScore || 0
    };
  });
}

/**
 * Enrich votes with comprehensive related data
 * @param ctx - Database context
 * @param votes - Array of vote objects
 * @returns Array of votes with comprehensive enriched data
 */
export async function enrichVotesWithComprehensiveData(
  ctx: any,
  votes: any[]
): Promise<any[]> {
  if (votes.length === 0) {
    return [];
  }

  // Extract unique IDs for batch loading
  const userIds = extractUniqueIds(votes, "userId");
  const ideaIds = extractUniqueIds(votes, "ideaId");
  const eventIds = extractUniqueIds(votes, "eventId");
  const sessionIds = extractUniqueIds(votes, "sessionId");

  // Batch load all related entities
  const [users, ideas, events, sessions] = await Promise.all([
    batchLoadUsers(ctx, userIds),
    batchLoadIdeas(ctx, ideaIds),
    batchLoadEvents(ctx, eventIds),
    batchLoadSessions(ctx, sessionIds)
  ]);

  // Get team IDs from ideas for team information
  const teamIds = Array.from(ideas.values())
    .filter(idea => idea !== null)
    .map(idea => idea.teamId);

  const teams = await batchLoadTeams(ctx, teamIds);

  // Enrich votes with all related data
  return votes.map(vote => {
    const user = users.get(vote.userId);
    const idea = ideas.get(vote.ideaId);
    const event = events.get(vote.eventId);
    const session = sessions.get(vote.sessionId);
    const team = idea ? teams.get(idea.teamId) : null;

    return {
      ...vote,
      userName: user?.name || "Unknown User",
      userUsername: user?.username || "Unknown",
      ideaName: idea?.name || "Unknown Idea",
      eventName: event?.name || "Unknown Event",
      sessionName: session?.name || "Unknown Session",
      teamName: team?.name || "Unknown Team"
    };
  });
}

/**
 * Enrich quickfire votes with comprehensive related data
 * @param ctx - Database context
 * @param quickfireVotes - Array of quickfire vote objects
 * @returns Array of quickfire votes with comprehensive enriched data
 */
export async function enrichQuickfireVotesWithComprehensiveData(
  ctx: any,
  quickfireVotes: any[]
): Promise<any[]> {
  if (quickfireVotes.length === 0) {
    return [];
  }

  // Extract unique IDs for batch loading
  const userIds = extractUniqueIds(quickfireVotes, "userId");
  const quickfireIds = extractUniqueIds(quickfireVotes, "quickfireId");

  // Batch load all related entities
  const [users, quickfires] = await Promise.all([
    batchLoadUsers(ctx, userIds),
    batchLoadQuickfires(ctx, quickfireIds)
  ]);

  // Get session and event IDs from quickfires
  const sessionIds = Array.from(quickfires.values())
    .filter(qf => qf !== null)
    .map(qf => qf.sessionId);

  const eventIds = Array.from(quickfires.values())
    .filter(qf => qf !== null)
    .map(qf => qf.eventId);

  const [sessions, events] = await Promise.all([
    batchLoadSessions(ctx, sessionIds),
    batchLoadEvents(ctx, eventIds)
  ]);

  // Enrich quickfire votes with all related data
  return quickfireVotes.map(vote => {
    const user = users.get(vote.userId);
    const quickfire = quickfires.get(vote.quickfireId);
    const session = quickfire ? sessions.get(quickfire.sessionId) : null;
    const event = quickfire ? events.get(quickfire.eventId) : null;

    return {
      ...vote,
      userName: user?.name || "Unknown User",
      userUsername: user?.username || "Unknown",
      quickfireIdea: quickfire?.idea || "Unknown Quickfire",
      quickfireQuestion: quickfire?.question || null,
      sessionName: session?.name || "Unknown Session",
      eventName: event?.name || "Unknown Event"
    };
  });
}

/**
 * Enrich spark submissions with comprehensive related data
 * @param ctx - Database context
 * @param submissions - Array of spark submission objects
 * @returns Array of spark submissions with comprehensive enriched data
 */
export async function enrichSparkSubmissionsWithComprehensiveData(
  ctx: any,
  submissions: any[]
): Promise<any[]> {
  if (submissions.length === 0) {
    return [];
  }

  // Extract unique IDs for batch loading
  const userIds = extractUniqueIds(submissions, "userId");
  const teamIds = extractUniqueIds(submissions, "teamId");
  const sparkIds = extractUniqueIds(submissions, "sparkId");
  const sessionIds = extractUniqueIds(submissions, "sessionId");
  const eventIds = extractUniqueIds(submissions, "eventId");

  // Batch load all related entities
  const [users, teams, sparks, sessions, events] = await Promise.all([
    batchLoadUsers(ctx, userIds),
    batchLoadTeams(ctx, teamIds),
    batchLoadSparks(ctx, sparkIds),
    batchLoadSessions(ctx, sessionIds),
    batchLoadEvents(ctx, eventIds)
  ]);

  // Enrich spark submissions with all related data
  return submissions.map(submission => {
    const user = users.get(submission.userId);
    const team = teams.get(submission.teamId);
    const spark = sparks.get(submission.sparkId);
    const session = sessions.get(submission.sessionId);
    const event = events.get(submission.eventId);

    return {
      ...submission,
      userName: user?.name || "Unknown User",
      userUsername: user?.username || "Unknown",
      teamName: team?.name || "Unknown Team",
      sparkName: spark?.name || "Unknown Spark",
      sparkDescription: spark?.description || null,
      sessionName: session?.name || "Unknown Session",
      eventName: event?.name || "Unknown Event"
    };
  });
}

/**
 * Enrich entities with presence information
 * @param ctx - Database context
 * @param entities - Array of entities with userId field
 * @returns Array of entities with presence information
 */
export async function enrichEntitiesWithPresence(
  ctx: any,
  entities: any[]
): Promise<any[]> {
  if (entities.length === 0) {
    return [];
  }

  // Extract unique user IDs
  const userIds = extractUniqueIds(entities, "userId");
  
  // Get presence information for all users more efficiently
  const presenceData = await batchLoadPresenceForUsers(ctx, userIds);

  // Enrich entities with presence information
  return entities.map(entity => {
    const presence = presenceData.get(entity.userId.toString());
    return {
      ...entity,
      isOnline: presence?.isOnline || false,
      lastSeen: presence?.lastSeen || null
    };
  });
}

/**
 * Batch load presence information for multiple users
 * @param ctx - Database context
 * @param userIds - Array of user IDs to get presence for
 * @returns Map of user ID to presence information
 */
export async function batchLoadPresenceForUsers(
  ctx: any,
  userIds: any[]
): Promise<Map<string, { isOnline: boolean; lastSeen: number | null }>> {
  const presenceMap = new Map();
  
  if (userIds.length === 0) {
    return presenceMap;
  }

  // Get all presence records for these users
  const allPresenceRecords = await Promise.all(
    userIds.map(async (userId) => {
      const records = await ctx.db
        .query("presence")
        .withIndex("by_user", (q: any) => q.eq("userId", userId))
        .collect();
      
      return { userId, records };
    })
  );

  // Process each user's presence records
  allPresenceRecords.forEach(({ userId, records }) => {
    // Find the most recent online presence
    const onlineRecords = records.filter((r: any) => r.isOnline);
    const mostRecentOnline = onlineRecords.reduce((latest: any, current: any) => 
      !latest || current.lastSeen > latest.lastSeen ? current : latest, null
    );

    // Check if user is considered online (within threshold)
    const now = Date.now();
    const isOnline = mostRecentOnline && 
      (now - mostRecentOnline.lastSeen) <= (3 * 60 * 1000); // 3 minutes

    presenceMap.set(userId.toString(), {
      isOnline: !!isOnline,
      lastSeen: mostRecentOnline?.lastSeen || null
    });
  });

  return presenceMap;
}

/**
 * Enrich event data with comprehensive statistics
 * @param ctx - Database context
 * @param events - Array of event objects
 * @returns Array of events with comprehensive statistics
 */
export async function enrichEventsWithStatistics(
  ctx: any,
  events: any[]
): Promise<any[]> {
  if (events.length === 0) {
    return [];
  }

  // Extract event IDs for batch loading
  const eventIds = events.map(event => event._id);
  
  // Use optimized batch loading for basic statistics
  const basicStatsMap = await batchLoadEventStatistics(ctx, eventIds);
  
  // Get additional statistics that require more complex processing
  const additionalStatsPromises = eventIds.map(async (eventId) => {
    const [votes, sparkSubmissions] = await Promise.all([
      ctx.db.query("votes").withIndex("by_event", (q: any) => q.eq("eventId", eventId)).collect(),
      ctx.db.query("sparkSubmissions").withIndex("by_event", (q: any) => q.eq("eventId", eventId)).collect()
    ]);

    // Get event users
    const allUsers = await ctx.db.query("users").collect();
    const eventUsers = allUsers.filter((user: any) => 
      user.events?.some((e: any) => e.eventId === eventId)
    );

    const uniqueVoters = new Set(votes.map((vote: any) => vote.userId.toString())).size;
    const uniqueSparkParticipants = new Set(sparkSubmissions.map((sub: any) => sub.userId.toString())).size;

    return {
      eventId,
      uniqueVoters,
      uniqueSparkParticipants,
      votingParticipationRate: eventUsers.length > 0 ? (uniqueVoters / eventUsers.length) * 100 : 0,
      sparkParticipationRate: eventUsers.length > 0 ? (uniqueSparkParticipants / eventUsers.length) * 100 : 0
    };
  });

  const additionalStats = await Promise.all(additionalStatsPromises);
  const additionalStatsMap = new Map(
    additionalStats.map(stats => [stats.eventId.toString(), stats])
  );

  // Enrich events with combined statistics
  return events.map(event => {
    const basicStats = basicStatsMap.get(event._id);
    const additionalStats = additionalStatsMap.get(event._id.toString());
    
    return {
      ...event,
      statistics: {
        ...basicStats,
        uniqueVoters: additionalStats?.uniqueVoters || 0,
        uniqueSparkParticipants: additionalStats?.uniqueSparkParticipants || 0,
        votingParticipationRate: additionalStats?.votingParticipationRate || 0,
        sparkParticipationRate: additionalStats?.sparkParticipationRate || 0
      }
    };
  });
}

/**
 * Enrich any collection with user and team information
 * @param ctx - Database context
 * @param entities - Array of entities with userId and teamId fields
 * @returns Array of entities with user and team information
 */
export async function enrichWithUserAndTeamInfo(
  ctx: any,
  entities: any[]
): Promise<any[]> {
  if (entities.length === 0) {
    return [];
  }

  // Extract unique IDs for batch loading
  const userIds = extractUniqueIds(entities, "userId");
  const teamIds = extractUniqueIds(entities, "teamId");

  // Batch load users and teams
  const [users, teams] = await Promise.all([
    batchLoadUsers(ctx, userIds),
    batchLoadTeams(ctx, teamIds)
  ]);

  // Enrich entities with user and team information
  return entities.map(entity => {
    const user = users.get(entity.userId);
    const team = teams.get(entity.teamId);

    return {
      ...entity,
      userName: user?.name || "Unknown User",
      userUsername: user?.username || "Unknown",
      teamName: team?.name || "Unknown Team"
    };
  });
}

/**
 * Enrich entities with session and event context
 * @param ctx - Database context
 * @param entities - Array of entities with sessionId and eventId fields
 * @returns Array of entities with session and event information
 */
export async function enrichWithSessionAndEventContext(
  ctx: any,
  entities: any[]
): Promise<any[]> {
  if (entities.length === 0) {
    return [];
  }

  // Extract unique IDs for batch loading
  const sessionIds = extractUniqueIds(entities, "sessionId");
  const eventIds = extractUniqueIds(entities, "eventId");

  // Batch load sessions and events
  const [sessions, events] = await Promise.all([
    batchLoadSessions(ctx, sessionIds),
    batchLoadEvents(ctx, eventIds)
  ]);

  // Enrich entities with session and event information
  return entities.map(entity => {
    const session = sessions.get(entity.sessionId);
    const event = events.get(entity.eventId);

    return {
      ...entity,
      sessionName: session?.name || "Unknown Session",
      eventName: event?.name || "Unknown Event"
    };
  });
}

/**
 * Enrich entities with timestamp formatting
 * @param ctx - Database context
 * @param entities - Array of entities with timestamp fields
 * @returns Array of entities with formatted timestamps
 */
export async function enrichWithFormattedTimestamps(
  _ctx: any,
  entities: any[]
): Promise<any[]> {
  if (entities.length === 0) {
    return [];
  }

  return entities.map(entity => {
    const formatTimestamp = (timestamp: number | undefined) => {
      if (!timestamp) return null;
      return new Date(timestamp).toISOString();
    };

    return {
      ...entity,
      createdAtFormatted: formatTimestamp(entity.createdAt),
      updatedAtFormatted: formatTimestamp(entity.updatedAt),
      submittedAtFormatted: formatTimestamp(entity.submittedAt)
    };
  });
}

/**
 * Batch enrich multiple entity types with their respective relationships
 * @param ctx - Database context
 * @param entityCollections - Object containing different entity collections
 * @returns Object with enriched entity collections
 */
export async function batchEnrichEntityCollections(
  ctx: any,
  entityCollections: {
    ideas?: any[];
    votes?: any[];
    quickfireVotes?: any[];
    sparkSubmissions?: any[];
    users?: any[];
    teams?: any[];
    sessions?: any[];
    events?: any[];
  }
): Promise<{
  ideas?: any[];
  votes?: any[];
  quickfireVotes?: any[];
  sparkSubmissions?: any[];
  users?: any[];
  teams?: any[];
  sessions?: any[];
  events?: any[];
}> {
  const enrichedCollections: any = {};

  // Enrich each collection type appropriately
  if (entityCollections.ideas) {
    enrichedCollections.ideas = await enrichIdeasWithComprehensiveData(ctx, entityCollections.ideas);
  }

  if (entityCollections.votes) {
    enrichedCollections.votes = await enrichVotesWithComprehensiveData(ctx, entityCollections.votes);
  }

  if (entityCollections.quickfireVotes) {
    enrichedCollections.quickfireVotes = await enrichQuickfireVotesWithComprehensiveData(ctx, entityCollections.quickfireVotes);
  }

  if (entityCollections.sparkSubmissions) {
    enrichedCollections.sparkSubmissions = await enrichSparkSubmissionsWithComprehensiveData(ctx, entityCollections.sparkSubmissions);
  }

  if (entityCollections.users && entityCollections.events && entityCollections.events.length > 0) {
    enrichedCollections.users = await enrichUsersWithTeamInfo(ctx, entityCollections.users, entityCollections.events[0]._id);
  }

  if (entityCollections.teams && entityCollections.events && entityCollections.events.length > 0) {
    enrichedCollections.teams = await enrichTeamsWithMemberInfo(ctx, entityCollections.teams, entityCollections.events[0]._id);
  }

  if (entityCollections.sessions) {
    enrichedCollections.sessions = await enrichSessionsWithSparkInfo(ctx, entityCollections.sessions);
  }

  if (entityCollections.events) {
    enrichedCollections.events = await enrichEventsWithStatistics(ctx, entityCollections.events);
  }

  return enrichedCollections;
}