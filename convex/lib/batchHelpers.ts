/**
 * Batch loading utilities to eliminate N+1 queries
 * 
 * These utilities provide efficient batch loading patterns for frequently accessed
 * entities, reducing the number of database queries and improving performance.
 */

import { QueryCtx } from "../_generated/server";
import { Id } from "../_generated/dataModel";

/**
 * Batch load multiple entities by their IDs
 * @param ctx - Database context
 * @param ids - Array of entity IDs to load
 * @param tableName - Name of the table to query
 * @returns Map of ID to entity (or null if not found)
 */
export async function batchLoadEntities<T>(
  ctx: QueryCtx,
  ids: Id<any>[],
  _tableName: string
): Promise<Map<Id<any>, T | null>> {
  const results = new Map<Id<any>, T | null>();
  
  // Remove duplicates and filter out invalid IDs
  const uniqueIds = Array.from(new Set(ids)).filter(id => id != null);
  
  if (uniqueIds.length === 0) {
    return results;
  }
  
  // Batch load all entities
  const entities = await Promise.all(
    uniqueIds.map(id => ctx.db.get(id))
  );
  
  // Map results back to IDs
  uniqueIds.forEach((id, index) => {
    results.set(id, entities[index] as T | null);
  });
  
  return results;
}

/**
 * Batch load users by IDs
 * @param ctx - Database context
 * @param userIds - Array of user IDs
 * @returns Map of user ID to user entity
 */
export async function batchLoadUsers(
  ctx: QueryCtx,
  userIds: Id<"users">[]
): Promise<Map<Id<"users">, any | null>> {
  return batchLoadEntities(ctx, userIds, "users");
}

/**
 * Batch load teams by IDs
 * @param ctx - Database context
 * @param teamIds - Array of team IDs
 * @returns Map of team ID to team entity
 */
export async function batchLoadTeams(
  ctx: QueryCtx,
  teamIds: Id<"teams">[]
): Promise<Map<Id<"teams">, any | null>> {
  return batchLoadEntities(ctx, teamIds, "teams");
}

/**
 * Batch load sessions by IDs
 * @param ctx - Database context
 * @param sessionIds - Array of session IDs
 * @returns Map of session ID to session entity
 */
export async function batchLoadSessions(
  ctx: QueryCtx,
  sessionIds: Id<"sessions">[]
): Promise<Map<Id<"sessions">, any | null>> {
  return batchLoadEntities(ctx, sessionIds, "sessions");
}

/**
 * Batch load ideas by IDs
 * @param ctx - Database context
 * @param ideaIds - Array of idea IDs
 * @returns Map of idea ID to idea entity
 */
export async function batchLoadIdeas(
  ctx: QueryCtx,
  ideaIds: Id<"ideas">[]
): Promise<Map<Id<"ideas">, any | null>> {
  return batchLoadEntities(ctx, ideaIds, "ideas");
}

/**
 * Batch load events by IDs
 * @param ctx - Database context
 * @param eventIds - Array of event IDs
 * @returns Map of event ID to event entity
 */
export async function batchLoadEvents(
  ctx: QueryCtx,
  eventIds: Id<"events">[]
): Promise<Map<Id<"events">, any | null>> {
  return batchLoadEntities(ctx, eventIds, "events");
}

/**
 * Batch load sparks by IDs
 * @param ctx - Database context
 * @param sparkIds - Array of spark IDs
 * @returns Map of spark ID to spark entity
 */
export async function batchLoadSparks(
  ctx: QueryCtx,
  sparkIds: Id<"sparks">[]
): Promise<Map<Id<"sparks">, any | null>> {
  return batchLoadEntities(ctx, sparkIds, "sparks");
}

/**
 * Batch load quickfires by IDs
 * @param ctx - Database context
 * @param quickfireIds - Array of quickfire IDs
 * @returns Map of quickfire ID to quickfire entity
 */
export async function batchLoadQuickfires(
  ctx: QueryCtx,
  quickfireIds: Id<"quickfires">[]
): Promise<Map<Id<"quickfires">, any | null>> {
  return batchLoadEntities(ctx, quickfireIds, "quickfires");
}

/**
 * Batch load snippets by IDs
 * @param ctx - Database context
 * @param snippetIds - Array of snippet IDs
 * @returns Map of snippet ID to snippet entity
 */
export async function batchLoadSnippets(
  ctx: QueryCtx,
  snippetIds: Id<"snippets">[]
): Promise<Map<Id<"snippets">, any | null>> {
  return batchLoadEntities(ctx, snippetIds, "snippets");
}

/**
 * Enhanced batch loading with relationship resolution
 * Loads entities and their related data in a single batch operation
 */

/**
 * Batch load ideas with their related user and team data
 * @param ctx - Database context
 * @param ideaIds - Array of idea IDs
 * @returns Array of enriched idea objects with user and team data
 */
export async function batchLoadIdeasWithRelations(
  ctx: QueryCtx,
  ideaIds: Id<"ideas">[]
): Promise<Array<{
  idea: any;
  user: any | null;
  team: any | null;
  session: any | null;
}>> {
  const ideas = await batchLoadIdeas(ctx, ideaIds);
  
  // Extract unique user, team, and session IDs
  const userIds = new Set<Id<"users">>();
  const teamIds = new Set<Id<"teams">>();
  const sessionIds = new Set<Id<"sessions">>();
  
  Array.from(ideas.values()).forEach(idea => {
    if (idea) {
      userIds.add(idea.userId);
      teamIds.add(idea.teamId);
      sessionIds.add(idea.sessionId);
    }
  });
  
  // Batch load all related entities
  const [users, teams, sessions] = await Promise.all([
    batchLoadUsers(ctx, Array.from(userIds)),
    batchLoadTeams(ctx, Array.from(teamIds)),
    batchLoadSessions(ctx, Array.from(sessionIds))
  ]);
  
  // Combine data
  return ideaIds.map(id => {
    const idea = ideas.get(id);
    return {
      idea,
      user: idea ? users.get(idea.userId) : null,
      team: idea ? teams.get(idea.teamId) : null,
      session: idea ? sessions.get(idea.sessionId) : null
    };
  });
}

/**
 * Batch load votes with their related idea and user data
 * @param ctx - Database context
 * @param voteIds - Array of vote IDs
 * @returns Array of enriched vote objects with idea and user data
 */
export async function batchLoadVotesWithRelations(
  ctx: QueryCtx,
  voteIds: Id<"votes">[]
): Promise<Array<{
  vote: any;
  idea: any | null;
  user: any | null;
}>> {
  const votes = await batchLoadEntities<any>(ctx, voteIds, "votes");
  
  // Extract unique idea and user IDs
  const ideaIds = new Set<Id<"ideas">>();
  const userIds = new Set<Id<"users">>();
  
  Array.from(votes.values()).forEach(vote => {
    if (vote) {
      ideaIds.add(vote.ideaId);
      userIds.add(vote.userId);
    }
  });
  
  // Batch load all related entities
  const [ideas, users] = await Promise.all([
    batchLoadIdeas(ctx, Array.from(ideaIds)),
    batchLoadUsers(ctx, Array.from(userIds))
  ]);
  
  // Combine data
  return voteIds.map(id => {
    const vote = votes.get(id);
    return {
      vote,
      idea: vote ? ideas.get(vote.ideaId) : null,
      user: vote ? users.get(vote.userId) : null
    };
  });
}

/**
 * Batch load team members for multiple teams
 * @param ctx - Database context
 * @param eventId - Event ID to filter users
 * @param teamIds - Array of team IDs
 * @returns Map of team ID to array of team members
 */
export async function batchLoadTeamMembers(
  ctx: QueryCtx,
  eventId: Id<"events">,
  teamIds: Id<"teams">[]
): Promise<Map<Id<"teams">, any[]>> {
  const result = new Map<Id<"teams">, any[]>();
  
  if (teamIds.length === 0) {
    return result;
  }
  
  // Get all users for the event
  const allUsers = await ctx.db.query("users").collect();
  
  // Group users by team
  teamIds.forEach(teamId => {
    const teamMembers = allUsers.filter(user => 
      user.events?.some(event => 
        event.eventId === eventId && event.teamId === teamId
      )
    );
    result.set(teamId, teamMembers);
  });
  
  return result;
}

/**
 * Batch load user teams for multiple users in an event
 * @param ctx - Database context
 * @param eventId - Event ID to filter teams
 * @param userIds - Array of user IDs
 * @returns Map of user ID to their team in the event
 */
export async function batchLoadUserTeams(
  ctx: QueryCtx,
  eventId: Id<"events">,
  userIds: Id<"users">[]
): Promise<Map<Id<"users">, any | null>> {
  const result = new Map<Id<"users">, any | null>();
  
  if (userIds.length === 0) {
    return result;
  }
  
  // Batch load users
  const users = await batchLoadUsers(ctx, userIds);
  
  // Get team IDs for the event
  const teamIds = new Set<Id<"teams">>();
  Array.from(users.values()).forEach(user => {
    if (user) {
      const eventData = user.events?.find((e: any) => e.eventId === eventId);
      if (eventData?.teamId) {
        teamIds.add(eventData.teamId);
      }
    }
  });
  
  // Batch load teams
  const teams = await batchLoadTeams(ctx, Array.from(teamIds));
  
  // Map users to their teams
  userIds.forEach(userId => {
    const user = users.get(userId);
    if (user) {
      const eventData = user.events?.find((e: any) => e.eventId === eventId);
      const team = eventData?.teamId ? teams.get(eventData.teamId) : null;
      result.set(userId, team);
    } else {
      result.set(userId, null);
    }
  });
  
  return result;
}

/**
 * Utility function to extract unique IDs from an array of objects
 * @param objects - Array of objects
 * @param idField - Field name containing the ID
 * @returns Array of unique IDs
 */
export function extractUniqueIds<T>(
  objects: T[],
  idField: keyof T
): Id<any>[] {
  return Array.from(new Set(objects.map(obj => obj[idField]).filter(id => id != null))) as unknown as Id<any>[];
}

/**
 * Utility function to group objects by a field value
 * @param objects - Array of objects to group
 * @param groupField - Field to group by
 * @returns Map of field value to array of objects
 */
export function groupByField<T, K extends keyof T>(
  objects: T[],
  groupField: K
): Map<T[K], T[]> {
  const grouped = new Map<T[K], T[]>();
  
  objects.forEach(obj => {
    const key = obj[groupField];
    if (!grouped.has(key)) {
      grouped.set(key, []);
    }
    grouped.get(key)!.push(obj);
  });
  
  return grouped;
}

/**
 * Batch load sessions using spark IDs
 * @param ctx - Database context
 * @param sparkIds - Array of spark IDs to find sessions for
 * @returns Map of spark ID to array of sessions using that spark
 */
export async function batchLoadSessionsBySpark(
  ctx: QueryCtx,
  sparkIds: Id<"sparks">[]
): Promise<Map<Id<"sparks">, any[]>> {
  const result = new Map<Id<"sparks">, any[]>();
  
  // Remove duplicates
  const uniqueSparkIds = Array.from(new Set(sparkIds)).filter(id => id != null);
  
  if (uniqueSparkIds.length === 0) {
    return result;
  }
  
  // Get all sessions that use any of these sparks
  const allSessions = await ctx.db
    .query("sessions")
    .withIndex("by_spark")
    .collect();
  
  // Group sessions by spark ID
  uniqueSparkIds.forEach(sparkId => {
    const sessionsForSpark = allSessions.filter(session => session.sparkId === sparkId);
    result.set(sparkId, sessionsForSpark);
  });
  
  return result;
}

/**
 * Batch delete multiple entities efficiently
 * @param ctx - Database context
 * @param entities - Array of entities to delete
 * @returns Promise that resolves when all entities are deleted
 */
export async function batchDeleteEntities(
  ctx: any,
  entities: any[]
): Promise<void> {
  if (entities.length === 0) {
    return;
  }
  
  // Delete in batches to avoid overwhelming the database
  const batchSize = 50;
  const batches = [];
  
  for (let i = 0; i < entities.length; i += batchSize) {
    batches.push(entities.slice(i, i + batchSize));
  }
  
  for (const batch of batches) {
    await Promise.all(batch.map(entity => ctx.db.delete(entity._id)));
  }
}

/**
 * Batch update multiple entities efficiently
 * @param ctx - Database context
 * @param updates - Array of update objects with id and updateData
 * @returns Promise that resolves when all entities are updated
 */
export async function batchUpdateEntities(
  ctx: any,
  updates: Array<{ id: string, updateData: any }>
): Promise<void> {
  if (updates.length === 0) {
    return;
  }
  
  const batchSize = 50;
  
  for (let i = 0; i < updates.length; i += batchSize) {
    const batch = updates.slice(i, i + batchSize);
    const updatePromises = batch.map(update => 
      ctx.db.patch(update.id, update.updateData)
    );
    await Promise.all(updatePromises);
  }
}

/**
 * Optimized batch operations for event-related statistics
 * @param ctx - Database context
 * @param eventIds - Array of event IDs to get statistics for
 * @returns Map of event ID to event statistics
 */
export async function batchLoadEventStatistics(
  ctx: QueryCtx,
  eventIds: Id<"events">[]
): Promise<Map<Id<"events">, {
  teamCount: number;
  sessionCount: number;
  ideaCount: number;
  voteCount: number;
  sparkSubmissionCount: number;
  userCount: number;
}>> {
  const statisticsMap = new Map();
  
  if (eventIds.length === 0) {
    return statisticsMap;
  }
  
  // Remove duplicates
  const uniqueEventIds = Array.from(new Set(eventIds));
  
  // Batch load all related statistics in parallel
  const statsPromises = uniqueEventIds.map(async (eventId) => {
    const [teams, sessions, ideas, votes, sparkSubmissions, allUsers] = await Promise.all([
      ctx.db.query("teams").withIndex("by_event", (q: any) => q.eq("eventId", eventId)).collect(),
      ctx.db.query("sessions").withIndex("by_event", (q: any) => q.eq("eventId", eventId)).collect(),
      ctx.db.query("ideas").withIndex("by_event", (q: any) => q.eq("eventId", eventId)).collect(),
      ctx.db.query("votes").withIndex("by_event", (q: any) => q.eq("eventId", eventId)).collect(),
      ctx.db.query("sparkSubmissions").withIndex("by_event", (q: any) => q.eq("eventId", eventId)).collect(),
      ctx.db.query("users").collect()
    ]);
    
    // Count users associated with this event
    const eventUsers = allUsers.filter((user: any) => 
      user.events?.some((e: any) => e.eventId === eventId)
    );
    
    return {
      eventId,
      statistics: {
        teamCount: teams.length,
        sessionCount: sessions.length,
        ideaCount: ideas.length,
        voteCount: votes.length,
        sparkSubmissionCount: sparkSubmissions.length,
        userCount: eventUsers.length
      }
    };
  });
  
  const results = await Promise.all(statsPromises);
  
  // Build result map
  results.forEach(({ eventId, statistics }) => {
    statisticsMap.set(eventId, statistics);
  });
  
  return statisticsMap;
}