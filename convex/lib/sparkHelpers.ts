/**
 * Spark query helpers for standardized database access patterns
 * 
 * These helpers provide consistent patterns for spark-related database operations,
 * including spark forms and submissions, reducing code duplication and optimizing query performance.
 */

import { Id } from "../_generated/dataModel";
import { 
  batchLoadUsers, 
  batchLoadTeams, 
  batchLoadSessions, 
  batchLoadEvents,
  batchLoadSparks,
  batchLoadSessionsBySpark,
  batchDeleteEntities,
  extractUniqueIds,
  groupByField
} from "./batchHelpers";
import { getActiveEventOrNull } from "./eventHelpers";
import { getActiveSessionForActiveEvent } from "./sessionHelpers";

/**
 * Get spark by ID with error handling
 * @param ctx - Database context
 * @param sparkId - Spark ID to retrieve
 * @returns Spark entity or null if not found
 */
export async function getSparkById(
  ctx: any,
  sparkId: Id<"sparks">
): Promise<any | null> {
  return await ctx.db.get(sparkId);
}

/**
 * Get spark by ID or throw error if not found
 * @param ctx - Database context
 * @param sparkId - Spark ID to retrieve
 * @returns Spark entity
 * @throws Error if spark not found
 */
export async function getSparkByIdOrThrow(
  ctx: any,
  sparkId: Id<"sparks">
): Promise<any> {
  const spark = await ctx.db.get(sparkId);
  if (!spark) {
    const { ConvexError } = require("convex/values");
    throw new ConvexError({
      message: `Spark with ID ${sparkId} not found`,
      code: "RESOURCE_NOT_FOUND"
    });
  }
  return spark;
}

/**
 * Get all sparks for an event ordered by creation date
 * @param ctx - Database context
 * @param eventId - Event ID to get sparks for
 * @returns Array of sparks ordered by creation date
 */
export async function getAllSparks(
  ctx: any,
  eventId: Id<"events">
): Promise<any[]> {
  return await ctx.db
    .query("sparks")
    .withIndex("by_event", (q: any) => q.eq("eventId", eventId))
    .collect();
}

/**
 * Get spark by name using optimized index
 * @param ctx - Database context
 * @param name - Spark name to find
 * @param eventId - Event ID to filter by
 * @returns Spark entity or null if not found
 */
export async function getSparkByName(
  ctx: any,
  name: string,
  eventId: Id<"events">
): Promise<any | null> {
  return await ctx.db
    .query("sparks")
    .withIndex("by_event_name", (q: any) => q.eq("eventId", eventId).eq("name", name))
    .first();
}

/**
 * Get spark submission by ID with error handling
 * @param ctx - Database context
 * @param submissionId - Submission ID to retrieve
 * @returns Spark submission entity or null if not found
 */
export async function getSparkSubmissionById(
  ctx: any,
  submissionId: Id<"sparkSubmissions">
): Promise<any | null> {
  return await ctx.db.get(submissionId);
}

/**
 * Get spark submission by ID or throw error if not found
 * @param ctx - Database context
 * @param submissionId - Submission ID to retrieve
 * @returns Spark submission entity
 * @throws Error if submission not found
 */
export async function getSparkSubmissionByIdOrThrow(
  ctx: any,
  submissionId: Id<"sparkSubmissions">
): Promise<any> {
  const submission = await ctx.db.get(submissionId);
  if (!submission) {
    const { ConvexError } = require("convex/values");
    throw new ConvexError({
      message: `Spark submission with ID ${submissionId} not found`,
      code: "RESOURCE_NOT_FOUND"
    });
  }
  return submission;
}

/**
 * Get spark submissions by spark using optimized index
 * @param ctx - Database context
 * @param sparkId - Spark ID to get submissions for
 * @returns Array of submissions for the spark
 */
export async function getSparkSubmissionsBySpark(
  ctx: any,
  sparkId: Id<"sparks">
): Promise<any[]> {
  return await ctx.db
    .query("sparkSubmissions")
    .withIndex("by_spark", (q: any) => q.eq("sparkId", sparkId))
    .collect();
}

/**
 * Get spark submissions by session using optimized index
 * @param ctx - Database context
 * @param sessionId - Session ID to get submissions for
 * @returns Array of submissions for the session
 */
export async function getSparkSubmissionsBySession(
  ctx: any,
  sessionId: Id<"sessions">
): Promise<any[]> {
  return await ctx.db
    .query("sparkSubmissions")
    .withIndex("by_session", (q: any) => q.eq("sessionId", sessionId))
    .collect();
}

/**
 * Get spark submissions by user using optimized index
 * @param ctx - Database context
 * @param userId - User ID to get submissions for
 * @returns Array of submissions by the user
 */
export async function getSparkSubmissionsByUser(
  ctx: any,
  userId: Id<"users">
): Promise<any[]> {
  return await ctx.db
    .query("sparkSubmissions")
    .withIndex("by_user", (q: any) => q.eq("userId", userId))
    .collect();
}

/**
 * Get spark submissions by team using optimized index
 * @param ctx - Database context
 * @param teamId - Team ID to get submissions for
 * @returns Array of submissions by the team
 */
export async function getSparkSubmissionsByTeam(
  ctx: any,
  teamId: Id<"teams">
): Promise<any[]> {
  return await ctx.db
    .query("sparkSubmissions")
    .withIndex("by_team", (q: any) => q.eq("teamId", teamId))
    .collect();
}

/**
 * Get spark submissions by event using optimized index
 * @param ctx - Database context
 * @param eventId - Event ID to get submissions for
 * @returns Array of submissions for the event
 */
export async function getSparkSubmissionsByEvent(
  ctx: any,
  eventId: Id<"events">
): Promise<any[]> {
  return await ctx.db
    .query("sparkSubmissions")
    .withIndex("by_event", (q: any) => q.eq("eventId", eventId))
    .collect();
}

/**
 * Get user's spark submission for a session
 * @param ctx - Database context
 * @param userId - User ID to get submission for
 * @param sessionId - Session ID to filter by
 * @returns User's spark submission for the session or null if not found
 */
export async function getUserSparkSubmissionForSession(
  ctx: any,
  userId: Id<"users">,
  sessionId: Id<"sessions">
): Promise<any | null> {
  return await ctx.db
    .query("sparkSubmissions")
    .withIndex("by_user_session", (q: any) => q.eq("userId", userId).eq("sessionId", sessionId))
    .first();
}

/**
 * Get spark submissions with enriched data (user, team, spark details)
 * @param ctx - Database context
 * @param submissions - Array of spark submission objects
 * @returns Array of submissions with enriched data
 */
export async function enrichSparkSubmissionsWithDetails(
  ctx: any,
  submissions: any[]
): Promise<any[]> {
  if (submissions.length === 0) {
    return [];
  }

  // Extract unique IDs for batch loading
  const userIds = extractUniqueIds(submissions, "userId");
  const teamIds = extractUniqueIds(submissions, "teamId");
  const sparkIds = extractUniqueIds(submissions, "sparkId");
  const sessionIds = extractUniqueIds(submissions, "sessionId");
  const eventIds = extractUniqueIds(submissions, "eventId");

  // Batch load all related entities
  const [users, teams, sparks, sessions, events] = await Promise.all([
    batchLoadUsers(ctx, userIds),
    batchLoadTeams(ctx, teamIds),
    batchLoadSparks(ctx, sparkIds),
    batchLoadSessions(ctx, sessionIds),
    batchLoadEvents(ctx, eventIds)
  ]);

  // Enrich submissions with details
  return submissions.map(submission => {
    const user = users.get(submission.userId);
    const team = teams.get(submission.teamId);
    const spark = sparks.get(submission.sparkId);
    const session = sessions.get(submission.sessionId);
    const event = events.get(submission.eventId);

    return {
      ...submission,
      userName: user?.name || "Unknown User",
      userUsername: user?.username || "Unknown",
      teamName: team?.name || "Unknown Team",
      sparkName: spark?.name || "Unknown Spark",
      sessionName: session?.name || "Unknown Session",
      eventName: event?.name || "Unknown Event"
    };
  });
}

/**
 * Get spark submissions grouped by team
 * @param ctx - Database context
 * @param sparkId - Spark ID to get submissions for
 * @returns Object with team IDs as keys and submission arrays as values
 */
export async function getSparkSubmissionsGroupedByTeam(
  ctx: any,
  sparkId: Id<"sparks">
): Promise<Record<string, any[]>> {
  const submissions = await getSparkSubmissionsBySpark(ctx, sparkId);
  const enrichedSubmissions = await enrichSparkSubmissionsWithDetails(ctx, submissions);
  
  const grouped = groupByField(enrichedSubmissions, "teamId");
  const result: Record<string, any[]> = {};
  
  Array.from(grouped.entries()).forEach(([key, value]) => {
    result[key.toString()] = value;
  });
  
  return result;
}

/**
 * Get spark submissions grouped by session
 * @param ctx - Database context
 * @param eventId - Event ID to get submissions for
 * @returns Object with session IDs as keys and submission arrays as values
 */
export async function getSparkSubmissionsGroupedBySession(
  ctx: any,
  eventId: Id<"events">
): Promise<Record<string, any[]>> {
  const submissions = await getSparkSubmissionsByEvent(ctx, eventId);
  const enrichedSubmissions = await enrichSparkSubmissionsWithDetails(ctx, submissions);
  
  const grouped = groupByField(enrichedSubmissions, "sessionId");
  const result: Record<string, any[]> = {};
  
  Array.from(grouped.entries()).forEach(([key, value]) => {
    result[key.toString()] = value;
  });
  
  return result;
}

/**
 * Get spark submission statistics for a spark
 * @param ctx - Database context
 * @param sparkId - Spark ID to get statistics for
 * @returns Object with submission statistics
 */
export async function getSparkSubmissionStats(
  ctx: any,
  sparkId: Id<"sparks">
): Promise<{
  totalSubmissions: number;
  uniqueUsers: number;
  uniqueTeams: number;
  latestSubmission: number | null;
}> {
  const submissions = await getSparkSubmissionsBySpark(ctx, sparkId);
  
  const uniqueUsers = new Set(submissions.map(s => s.userId.toString())).size;
  const uniqueTeams = new Set(submissions.map(s => s.teamId.toString())).size;
  const latestSubmission = submissions.length > 0 
    ? Math.max(...submissions.map(s => s.submittedAt))
    : null;
  
  return {
    totalSubmissions: submissions.length,
    uniqueUsers,
    uniqueTeams,
    latestSubmission
  };
}

/**
 * Get spark submission statistics for an event
 * @param ctx - Database context
 * @param eventId - Event ID to get statistics for
 * @returns Object with event submission statistics
 */
export async function getSparkSubmissionStatsByEvent(
  ctx: any,
  eventId: Id<"events">
): Promise<{
  totalSubmissions: number;
  uniqueUsers: number;
  uniqueTeams: number;
  uniqueSparks: number;
  participationRate: number;
}> {
  const submissions = await getSparkSubmissionsByEvent(ctx, eventId);
  
  // Get event users for participation rate calculation
  const allUsers = await ctx.db.query("users").collect();
  const eventUsers = allUsers.filter((user: any) => 
    user.events?.some((event: any) => event.eventId === eventId)
  );
  
  const uniqueUsers = new Set(submissions.map(s => s.userId.toString())).size;
  const uniqueTeams = new Set(submissions.map(s => s.teamId.toString())).size;
  const uniqueSparks = new Set(submissions.map(s => s.sparkId.toString())).size;
  const participationRate = eventUsers.length > 0 ? (uniqueUsers / eventUsers.length) * 100 : 0;
  
  return {
    totalSubmissions: submissions.length,
    uniqueUsers,
    uniqueTeams,
    uniqueSparks,
    participationRate
  };
}

/**
 * Get spark submissions for active session
 * @param ctx - Database context
 * @param userId - User ID to get submissions for (optional)
 * @returns Array of submissions for the active session
 */
export async function getSparkSubmissionsForActiveSession(
  ctx: any,
  userId?: Id<"users">
): Promise<any[]> {
  const [activeEvent, activeSession] = await Promise.all([
    getActiveEventOrNull(ctx),
    getActiveSessionForActiveEvent(ctx)
  ]);

  if (!activeEvent || !activeSession) {
    return [];
  }

  if (userId) {
    // Get specific user's submission for the active session
    const submission = await getUserSparkSubmissionForSession(ctx, userId, activeSession._id);
    return submission ? [submission] : [];
  }

  // Get all submissions for the active session
  return await getSparkSubmissionsBySession(ctx, activeSession._id);
}

/**
 * Get spark form fields with validation
 * @param ctx - Database context
 * @param sparkId - Spark ID to get fields for
 * @returns Array of spark form fields
 */
export async function getSparkFormFields(
  ctx: any,
  sparkId: Id<"sparks">
): Promise<any[]> {
  const spark = await getSparkById(ctx, sparkId);
  
  if (!spark) {
    return [];
  }
  
  return spark.fields || [];
}

/**
 * Validate spark submission data against form fields
 * @param ctx - Database context
 * @param sparkId - Spark ID to validate against
 * @param submissionData - Submission data to validate
 * @returns Object with validation results
 */
export async function validateSparkSubmissionData(
  ctx: any,
  sparkId: Id<"sparks">,
  submissionData: any
): Promise<{
  isValid: boolean;
  errors: string[];
}> {
  const spark = await getSparkById(ctx, sparkId);
  
  if (!spark) {
    return {
      isValid: false,
      errors: ["Spark not found"]
    };
  }
  
  const errors: string[] = [];
  const fields = spark.fields || [];
  
  // Validate each field
  fields.forEach((field: any) => {
    const fieldValue = submissionData[field.label];
    
    // Check required fields
    if (field.required && (!fieldValue || fieldValue === "")) {
      errors.push(`${field.label} is required`);
    }
    
    // Validate dropdown and radio options
    if (field.type === "dropdown" || field.type === "radio") {
      if (fieldValue && field.options && !field.options.includes(fieldValue)) {
        errors.push(`Invalid value for ${field.label}`);
      }
    }
    
    // Validate checkbox fields
    if (field.type === "checkbox") {
      if (fieldValue !== undefined && typeof fieldValue !== "boolean") {
        errors.push(`${field.label} must be a boolean value`);
      }
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Get spark analytics data
 * @param ctx - Database context
 * @param sparkId - Spark ID to get analytics for
 * @returns Object with spark analytics data
 */
export async function getSparkAnalytics(
  ctx: any,
  sparkId: Id<"sparks">
): Promise<{
  spark: any;
  submissionStats: any;
  submissionsByTeam: Record<string, any[]>;
  fieldAnalytics: Record<string, any>;
}> {
  const spark = await getSparkByIdOrThrow(ctx, sparkId);
  const submissionStats = await getSparkSubmissionStats(ctx, sparkId);
  const submissionsByTeam = await getSparkSubmissionsGroupedByTeam(ctx, sparkId);
  
  // Analyze field data
  const submissions = await getSparkSubmissionsBySpark(ctx, sparkId);
  const fieldAnalytics: Record<string, any> = {};
  
  spark.fields?.forEach((field: any) => {
    const fieldValues = submissions
      .map(s => s.data[field.label])
      .filter(value => value !== undefined && value !== null && value !== "");
    
    fieldAnalytics[field.label] = {
      totalResponses: fieldValues.length,
      responseRate: submissions.length > 0 ? (fieldValues.length / submissions.length) * 100 : 0,
      uniqueValues: Array.from(new Set(fieldValues)).length,
      topValues: field.type === "dropdown" || field.type === "radio" 
        ? getTopValues(fieldValues, 5)
        : null
    };
  });
  
  return {
    spark,
    submissionStats,
    submissionsByTeam,
    fieldAnalytics
  };
}

/**
 * Helper function to get top values for field analytics
 * @param values - Array of field values
 * @param limit - Maximum number of top values to return
 * @returns Array of top values with counts
 */
function getTopValues(values: any[], limit: number = 5): Array<{ value: any; count: number }> {
  const counts: Record<string, number> = {};
  
  values.forEach(value => {
    const key = String(value);
    counts[key] = (counts[key] || 0) + 1;
  });
  
  return Object.entries(counts)
    .map(([value, count]) => ({ value, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, limit);
}

/**
 * Get team spark submission status
 * @param ctx - Database context
 * @param teamId - Team ID to check status for
 * @param sessionId - Session ID to check status for
 * @returns Object with team submission status
 */
export async function getTeamSparkSubmissionStatus(
  ctx: any,
  teamId: Id<"teams">,
  sessionId: Id<"sessions">
): Promise<{
  hasSubmission: boolean;
  submission: any | null;
  submissionCount: number;
}> {
  const submissions = await ctx.db
    .query("sparkSubmissions")
    .withIndex("by_team", (q: any) => q.eq("teamId", teamId))
    .filter((q: any) => q.eq(q.field("sessionId"), sessionId))
    .collect();
  
  return {
    hasSubmission: submissions.length > 0,
    submission: submissions[0] || null,
    submissionCount: submissions.length
  };
}

/**
 * Get all spark submissions for analytics/export
 * @param ctx - Database context
 * @returns Array of all spark submissions
 */
export async function getAllSparkSubmissions(ctx: any): Promise<any[]> {
  const submissions = await ctx.db.query("sparkSubmissions").collect();
  
  // Log warning for large collections
  if (submissions.length > 5000) {
    console.warn(`Large spark submissions collection: ${submissions.length} submissions`);
  }
  
  return submissions;
}

/**
 * Get spark submission summary for dashboard
 * @param ctx - Database context
 * @param eventId - Event ID to get summary for
 * @returns Object with spark submission summary
 */
export async function getSparkSubmissionSummary(
  ctx: any,
  eventId: Id<"events">
): Promise<{
  totalSubmissions: number;
  uniqueParticipants: number;
  activeSparks: number;
  participationRate: number;
  recentSubmissions: any[];
}> {
  const submissions = await getSparkSubmissionsByEvent(ctx, eventId);
  const sparks = await getAllSparks(ctx, eventId);
  
  // Get event users for participation rate calculation
  const allUsers = await ctx.db.query("users").collect();
  const eventUsers = allUsers.filter((user: any) => 
    user.events?.some((event: any) => event.eventId === eventId)
  );
  
  const uniqueParticipants = new Set(submissions.map(s => s.userId.toString())).size;
  const activeSparks = sparks.length; // All sparks are potentially active
  const participationRate = eventUsers.length > 0 ? (uniqueParticipants / eventUsers.length) * 100 : 0;
  
  // Get recent submissions (last 10)
  const recentSubmissions = submissions
    .sort((a, b) => b.submittedAt - a.submittedAt)
    .slice(0, 10);
  
  const enrichedRecent = await enrichSparkSubmissionsWithDetails(ctx, recentSubmissions);
  
  return {
    totalSubmissions: submissions.length,
    uniqueParticipants,
    activeSparks,
    participationRate,
    recentSubmissions: enrichedRecent
  };
}

/**
 * Check if a spark can be deleted (not being used by any sessions)
 * @param ctx - Database context
 * @param sparkId - Spark ID to check
 * @returns Boolean indicating if spark can be deleted
 */
export async function canDeleteSpark(
  ctx: any,
  sparkId: Id<"sparks">
): Promise<boolean> {
  const sessionsBySpark = await batchLoadSessionsBySpark(ctx, [sparkId]);
  const sessions = sessionsBySpark.get(sparkId) || [];
  
  return sessions.length === 0;
}

/**
 * Check if a spark can be edited (has no submissions)
 * @param ctx - Database context
 * @param sparkId - Spark ID to check
 * @returns Boolean indicating if spark can be edited
 */
export async function canEditSpark(
  ctx: any,
  sparkId: Id<"sparks">
): Promise<boolean> {
  const submissions = await getSparkSubmissionsBySpark(ctx, sparkId);
  
  return submissions.length === 0;
}

/**
 * Get spark submission count for a specific spark
 * @param ctx - Database context
 * @param sparkId - Spark ID to get count for
 * @returns Number of submissions for the spark
 */
export async function getSparkSubmissionCount(
  ctx: any,
  sparkId: Id<"sparks">
): Promise<number> {
  const submissions = await getSparkSubmissionsBySpark(ctx, sparkId);
  
  return submissions.length;
}

/**
 * Delete spark with optimized cascade deletion
 * @param ctx - Database context
 * @param sparkId - Spark ID to delete
 * @returns Promise that resolves when deletion is complete
 */
export async function deleteSparkWithCascade(
  ctx: any,
  sparkId: Id<"sparks">
): Promise<void> {
  // Get all submissions for this spark
  const submissions = await getSparkSubmissionsBySpark(ctx, sparkId);
  
  // Batch delete all submissions
  if (submissions.length > 0) {
    await batchDeleteEntities(ctx, submissions);
  }
  
  // Delete the spark itself
  await ctx.db.delete(sparkId);
}

/**
 * Get spark names with optimized query
 * @param ctx - Database context
 * @param eventId - Event ID to get sparks for
 * @returns Array of spark names with IDs and descriptions
 */
export async function getSparkNamesOptimized(
  ctx: any,
  eventId: Id<"events">
): Promise<Array<{
  _id: Id<"sparks">;
  name: string;
  description?: string;
}>> {
  const sparks = await getAllSparks(ctx, eventId);
  
  return sparks.map(spark => ({
    _id: spark._id,
    name: spark.name,
    description: spark.description,
  }));
}

/**
 * Validate spark field configuration
 * @param field - Field configuration to validate
 * @returns Array of validation errors
 */
export function validateSparkField(field: any): string[] {
  const errors: string[] = [];
  
  // Validate label
  if (!field.label?.trim()) {
    errors.push("All fields must have a label");
  }
  
  // Validate dropdown and radio fields have options
  if ((field.type === "dropdown" || field.type === "radio") && 
      (!field.options || field.options.length === 0)) {
    errors.push(`${field.type} fields must have at least one option`);
  }
  
  // Validate text and richtext fields don't have options
  if ((field.type === "text" || field.type === "richtext" || field.type === "checkbox") && 
      field.options && field.options.length > 0) {
    errors.push(`${field.type} fields should not have options`);
  }
  
  return errors;
}

/**
 * Validate array of spark fields
 * @param fields - Array of field configurations to validate
 * @returns Array of validation errors
 */
export function validateSparkFields(fields: any[]): string[] {
  const errors: string[] = [];
  
  // Check if fields array is empty
  if (fields.length === 0) {
    errors.push("Spark must have at least one field");
    return errors;
  }
  
  // Validate each field
  fields.forEach((field, index) => {
    const fieldErrors = validateSparkField(field);
    fieldErrors.forEach(error => {
      errors.push(`Field ${index + 1}: ${error}`);
    });
  });
  
  return errors;
}

/**
 * Batch check spark usability (can delete, can edit, submission count)
 * @param ctx - Database context
 * @param sparkIds - Array of spark IDs to check
 * @returns Map of spark ID to usability status
 */
export async function batchCheckSparkUsability(
  ctx: any,
  sparkIds: Id<"sparks">[]
): Promise<Map<Id<"sparks">, {
  canDelete: boolean;
  canEdit: boolean;
  submissionCount: number;
}>> {
  const result = new Map<Id<"sparks">, {
    canDelete: boolean;
    canEdit: boolean;
    submissionCount: number;
  }>();
  
  if (sparkIds.length === 0) {
    return result;
  }
  
  // Batch load sessions and submissions
  const [sessionsBySpark, submissionsBySpark] = await Promise.all([
    batchLoadSessionsBySpark(ctx, sparkIds),
    Promise.all(sparkIds.map(async (sparkId) => {
      const submissions = await getSparkSubmissionsBySpark(ctx, sparkId);
      return { sparkId, submissions };
    }))
  ]);
  
  // Create submissions map
  const submissionsMap = new Map<Id<"sparks">, any[]>();
  submissionsBySpark.forEach(({ sparkId, submissions }) => {
    submissionsMap.set(sparkId, submissions);
  });
  
  // Calculate usability for each spark
  sparkIds.forEach(sparkId => {
    const sessions = sessionsBySpark.get(sparkId) || [];
    const submissions = submissionsMap.get(sparkId) || [];
    
    result.set(sparkId, {
      canDelete: sessions.length === 0,
      canEdit: submissions.length === 0,
      submissionCount: submissions.length
    });
  });
  
  return result;
}

/**
 * Create a new spark with standardized data handling
 * @param ctx - Database context
 * @param sparkData - Spark data to create
 * @returns Created spark ID
 */
export async function createSpark(
  ctx: any,
  sparkData: {
    name: string;
    description?: string;
    eventId: Id<"events">;
    fields: any[];
  }
): Promise<Id<"sparks">> {
  const now = Date.now();
  
  return await ctx.db.insert("sparks", {
    name: sparkData.name.trim(),
    description: sparkData.description?.trim() || undefined,
    eventId: sparkData.eventId,
    fields: sparkData.fields,
    createdAt: now,
    updatedAt: now,
  });
}

/**
 * Update an existing spark with standardized data handling
 * @param ctx - Database context
 * @param sparkId - Spark ID to update
 * @param updateData - Update data
 * @returns Promise that resolves when update is complete
 */
export async function updateSpark(
  ctx: any,
  sparkId: Id<"sparks">,
  updateData: {
    name?: string;
    description?: string;
    color?: string;
    fields?: any[];
  }
): Promise<void> {
  const patchData: any = {
    updatedAt: Date.now(),
  };

  if (updateData.name !== undefined) {
    patchData.name = updateData.name.trim();
  }
  if (updateData.description !== undefined) {
    patchData.description = updateData.description?.trim() || undefined;
  }
  if (updateData.color !== undefined) {
    patchData.color = updateData.color;
  }
  if (updateData.fields !== undefined) {
    patchData.fields = updateData.fields;
  }

  await ctx.db.patch(sparkId, patchData);
}