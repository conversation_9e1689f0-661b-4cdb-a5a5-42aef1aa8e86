/**
 * Idea query helpers for standardized database access patterns
 * 
 * These helpers provide consistent patterns for idea-related database operations,
 * reducing code duplication and optimizing query performance.
 */

import { QueryCtx } from "../_generated/server";
import { Id } from "../_generated/dataModel";
import { 
  batchLoadUsers, 
  batchLoadTeams, 
  batchLoadSessions, 
  extractUniqueIds
} from "./batchHelpers";
import { getActiveEvent, getActiveEventOrNull } from "./eventHelpers";
import { getActiveSessionForActiveEvent } from "./sessionHelpers";

/**
 * Get idea by ID with error handling
 * @param ctx - Database context
 * @param ideaId - Idea ID to retrieve
 * @returns Idea entity or null if not found
 */
export async function getIdeaById(
  ctx: QueryCtx,
  ideaId: Id<"ideas">
): Promise<any | null> {
  return await ctx.db.get(ideaId);
}

/**
 * Get idea by ID or throw error if not found
 * @param ctx - Database context
 * @param ideaId - Idea ID to retrieve
 * @returns Idea entity
 * @throws Error if idea not found
 */
export async function getIdeaByIdOrThrow(
  ctx: QueryCtx,
  ideaId: Id<"ideas">
): Promise<any> {
  const idea = await ctx.db.get(ideaId);
  if (!idea) {
    throw new Error(`Idea with ID ${ideaId} not found`);
  }
  return idea;
}

/**
 * Get ideas by team and session using optimized compound index
 * @param ctx - Database context
 * @param teamId - Team ID to filter by
 * @param sessionId - Session ID to filter by
 * @returns Array of ideas for the team and session
 */
export async function getIdeasByTeamAndSession(
  ctx: QueryCtx,
  teamId: Id<"teams">,
  sessionId: Id<"sessions">
): Promise<any[]> {
  return await ctx.db
    .query("ideas")
    .withIndex("by_team_session", (q) => 
      q.eq("teamId", teamId).eq("sessionId", sessionId)
    )
    .collect();
}

/**
 * Get ideas by session using optimized index
 * @param ctx - Database context
 * @param sessionId - Session ID to filter by
 * @returns Array of ideas for the session
 */
export async function getIdeasBySession(
  ctx: QueryCtx,
  sessionId: Id<"sessions">
): Promise<any[]> {
  return await ctx.db
    .query("ideas")
    .withIndex("by_session", (q) => q.eq("sessionId", sessionId))
    .collect();
}

/**
 * Get ideas by event using optimized index
 * @param ctx - Database context
 * @param eventId - Event ID to filter by
 * @returns Array of ideas for the event
 */
export async function getIdeasByEvent(
  ctx: QueryCtx,
  eventId: Id<"events">
): Promise<any[]> {
  return await ctx.db
    .query("ideas")
    .withIndex("by_event", (q) => q.eq("eventId", eventId))
    .collect();
}

/**
 * Get submitted ideas by session using optimized compound index
 * @param ctx - Database context
 * @param sessionId - Session ID to filter by
 * @returns Array of submitted ideas for the session
 */
export async function getSubmittedIdeasBySession(
  ctx: QueryCtx,
  sessionId: Id<"sessions">
): Promise<any[]> {
  return await ctx.db
    .query("ideas")
    .withIndex("by_session_submitted", (q) => 
      q.eq("sessionId", sessionId).eq("submitted", true)
    )
    .collect();
}

/**
 * Get ideas by user using optimized index
 * @param ctx - Database context
 * @param userId - User ID to filter by
 * @returns Array of ideas created by the user
 */
export async function getIdeasByUser(
  ctx: QueryCtx,
  userId: Id<"users">
): Promise<any[]> {
  return await ctx.db
    .query("ideas")
    .withIndex("by_user", (q) => q.eq("userId", userId))
    .collect();
}

/**
 * Get ideas by team using optimized index
 * @param ctx - Database context
 * @param teamId - Team ID to filter by
 * @returns Array of ideas for the team
 */
export async function getIdeasByTeam(
  ctx: QueryCtx,
  teamId: Id<"teams">
): Promise<any[]> {
  return await ctx.db
    .query("ideas")
    .withIndex("by_team", (q) => q.eq("teamId", teamId))
    .collect();
}

/**
 * Get ideas by user and session using optimized compound index
 * @param ctx - Database context
 * @param userId - User ID to filter by
 * @param sessionId - Session ID to filter by
 * @returns Array of ideas for the user and session
 */
export async function getIdeasByUserAndSession(
  ctx: QueryCtx,
  userId: Id<"users">,
  sessionId: Id<"sessions">
): Promise<any[]> {
  // Use by_user index for better performance as users typically have fewer ideas than sessions
  return await ctx.db
    .query("ideas")
    .withIndex("by_user", (q) => q.eq("userId", userId))
    .filter((q) => q.eq(q.field("sessionId"), sessionId))
    .collect();
}

/**
 * Get ideas by current user for active session
 * @param ctx - Database context
 * @param userId - User ID
 * @returns Array of user's ideas for the active session
 */
export async function getIdeasByCurrentUser(
  ctx: QueryCtx,
  userId: Id<"users">
): Promise<any[]> {
  // Phase 1 Optimization: Parallelize user, event, and session lookups
  const [user, activeEvent, activeSession] = await Promise.all([
    ctx.db.get(userId),
    getActiveEventOrNull(ctx),
    getActiveSessionForActiveEvent(ctx)
  ]);

  if (!user || !activeEvent || !activeSession) {
    return [];
  }

  // Get ideas for this specific user and session using optimized index
  const ideas = await ctx.db
    .query("ideas")
    .withIndex("by_user", (q) => q.eq("userId", userId))
    .filter((q) => q.eq(q.field("sessionId"), activeSession._id))
    .collect();

  // Sort by updatedAt (most recent first), then by createdAt as fallback
  return ideas.sort((a, b) => {
    const aTime = a.updatedAt || a.createdAt;
    const bTime = b.updatedAt || b.createdAt;
    return bTime - aTime; // Descending order (newest first)
  });
}

/**
 * Get user ideas summary for active session (super optimized - uses .take(1))
 * Since ALL ideas are submitted together, we only need one idea to know submission status
 * @param ctx - Database context
 * @param userId - User ID
 * @param activeSession - Active session (passed to avoid duplicate query)
 * @returns Object with hasIdeas and hasSubmitted status
 */
export async function userHasIdeasForActiveSession(
  ctx: QueryCtx,
  userId: Id<"users">,
  activeSession: any
): Promise<{
  hasIdeas: boolean;
  hasSubmitted: boolean;
}> {
  if (!activeSession) {
    return { hasIdeas: false, hasSubmitted: false };
  }

  // ✅ Use .take(1) - most efficient! 
  // Since all ideas are submitted together, one idea tells us everything
  const ideas = await ctx.db
    .query("ideas")
    .withIndex("by_user", (q) => q.eq("userId", userId))
    .filter((q) => q.eq(q.field("sessionId"), activeSession._id))
    .take(1);

  const hasIdeas = ideas.length > 0;
  const hasSubmitted = hasIdeas ? ideas[0].submitted === true : false;

  return { hasIdeas, hasSubmitted };
}


/**
 * Get ideas with enriched data (user, team, session details)
 * @param ctx - Database context
 * @param ideas - Array of idea objects
 * @returns Array of ideas with enriched data
 */
export async function enrichIdeasWithDetails(
  ctx: QueryCtx,
  ideas: any[]
): Promise<any[]> {
  if (ideas.length === 0) {
    return [];
  }

  // Extract unique IDs for batch loading
  const userIds = extractUniqueIds(ideas, "userId");
  const teamIds = extractUniqueIds(ideas, "teamId");
  const sessionIds = extractUniqueIds(ideas, "sessionId");

  // Batch load all related entities
  const [users, teams, sessions] = await Promise.all([
    batchLoadUsers(ctx, userIds),
    batchLoadTeams(ctx, teamIds),
    batchLoadSessions(ctx, sessionIds)
  ]);

  // Enrich ideas with details
  return ideas.map(idea => {
    const user = users.get(idea.userId);
    const team = teams.get(idea.teamId);
    const session = sessions.get(idea.sessionId);

    return {
      ...idea,
      userName: user?.name || "Unknown",
      teamName: team?.name || "Unknown Team",
      sessionName: session?.name || "Unknown Session"
    };
  });
}

/**
 * Get ideas grouped by team with enriched data
 * @param ctx - Database context
 * @param sessionId - Optional session ID to filter by
 * @returns Object with team IDs as keys and team data as values
 */
export async function getIdeasGroupedByTeam(
  ctx: QueryCtx,
  sessionId?: Id<"sessions">
): Promise<Record<string, any>> {
  // Get active event
  const activeEvent = await getActiveEvent(ctx);

  // Get all teams for active event
  const teams = await ctx.db
    .query("teams")
    .withIndex("by_event", (q) => q.eq("eventId", activeEvent._id))
    .collect();

  // Get ideas - filter by session if specified
  let ideas: any[];
  
  if (sessionId) {
    ideas = await getIdeasBySession(ctx, sessionId);
  } else {
    // Get active session ideas
    const activeSession = await getActiveSessionForActiveEvent(ctx);
    if (!activeSession) {
      return {};
    }
    ideas = await getIdeasBySession(ctx, activeSession._id);
  }

  // Enrich ideas with user details
  const enrichedIdeas = await enrichIdeasWithDetails(ctx, ideas);

  // Group ideas by team
  const groupedIdeas: Record<string, any> = {};
  
  for (const idea of enrichedIdeas) {
    const team = teams.find(t => t._id === idea.teamId);
    if (!team) continue;

    if (!groupedIdeas[idea.teamId]) {
      groupedIdeas[idea.teamId] = {
        teamName: team.name,
        ideas: [],
        lastUpdate: 0
      };
    }

    const ideaWithDetails = {
      ...idea,
      teamName: team.name,
      eventId: activeEvent._id,
      submitted: idea.submitted || false,
      presenters: idea.presenters || []
    };

    groupedIdeas[idea.teamId].ideas.push(ideaWithDetails);
    groupedIdeas[idea.teamId].lastUpdate = Math.max(
      groupedIdeas[idea.teamId].lastUpdate, 
      idea.updatedAt || idea.submittedAt || idea.createdAt
    );
  }

  return groupedIdeas;
}

/**
 * Get team members for an idea's team
 * @param ctx - Database context
 * @param teamId - Team ID to get members for
 * @returns Array of team members with formatted data
 */
export async function getTeamMembers(
  ctx: QueryCtx,
  teamId: Id<"teams">
): Promise<Array<{
  id: string;
  name: string;
  username: string;
  role: string;
}>> {
  // Get the team
  const team = await ctx.db.get(teamId);
  if (!team) {
    return [];
  }

  // Get all users assigned to this team
  const users = await ctx.db.query("users").collect();
  const teamMembers = users.filter(user => 
    user.events?.some(event => event.teamId === teamId)
  );

  return teamMembers.map(user => ({
    id: user._id.toString(),
    name: user.name,
    username: user.username,
    role: user.role,
  }));
}

/**
 * Get ideas for voting - submitted ideas with enriched data
 * @param ctx - Database context
 * @param sessionId - Session ID to get ideas for
 * @returns Array of ideas ready for voting
 */
export async function getIdeasForVoting(
  ctx: QueryCtx,
  sessionId: Id<"sessions">
): Promise<any[]> {
  // Get submitted ideas for the session
  const ideas = await getSubmittedIdeasBySession(ctx, sessionId);
  
  // Enrich with user, team, and session details
  return await enrichIdeasWithDetails(ctx, ideas);
}

/**
 * Get top ideas by event with vote statistics
 * @param ctx - Database context
 * @param eventId - Event ID to get ideas for
 * @param limit - Maximum number of ideas to return
 * @returns Array of top-voted ideas
 */
export async function getTopIdeasByEvent(
  ctx: QueryCtx,
  eventId: Id<"events">,
  limit: number = 10
): Promise<any[]> {
  // Get all ideas for the event
  const ideas = await getIdeasByEvent(ctx, eventId);
  
  // Get vote statistics for each idea
  const ideasWithVotes = await Promise.all(
    ideas.map(async (idea) => {
      const votes = await ctx.db
        .query("votes")
        .withIndex("by_idea", (q) => q.eq("ideaId", idea._id))
        .collect();
      
      const totalScore = votes.reduce((sum, vote) => sum + vote.score, 0);
      const averageScore = votes.length > 0 ? totalScore / votes.length : 0;
      
      return {
        ...idea,
        voteCount: votes.length,
        totalScore,
        averageScore
      };
    })
  );
  
  // Sort by average score and return top ideas
  return ideasWithVotes
    .sort((a, b) => b.averageScore - a.averageScore)
    .slice(0, limit);
}

/**
 * Validate idea access for a user
 * @param ctx - Database context
 * @param ideaId - Idea ID to check access for
 * @param userId - User ID to check access for
 * @returns Boolean indicating if user has access
 */
export async function validateIdeaAccess(
  ctx: QueryCtx,
  ideaId: Id<"ideas">,
  userId: Id<"users">
): Promise<boolean> {
  const idea = await getIdeaById(ctx, ideaId);
  if (!idea) {
    return false;
  }

  // User can access their own ideas
  if (idea.userId === userId) {
    return true;
  }

  // Check if user is in the same team
  const user = await ctx.db.get(userId);
  if (!user) {
    return false;
  }

  // Find user's team for the idea's event
  const userEventData = user.events?.find((e: any) => e.eventId === idea.eventId);
  return userEventData?.teamId === idea.teamId;
}

/**
 * Get presenter details for an idea
 * @param ctx - Database context
 * @param presenterIds - Array of presenter user IDs
 * @returns Array of presenter details
 */
export async function getPresenterDetails(
  ctx: QueryCtx,
  presenterIds: Id<"users">[]
): Promise<Array<{ id: string; name: string }>> {
  if (presenterIds.length === 0) {
    return [];
  }

  // Batch load presenters
  const presenters = await batchLoadUsers(ctx, presenterIds);
  
  return presenterIds
    .map(id => {
      const presenter = presenters.get(id);
      return presenter ? {
        id: presenter._id.toString(),
        name: presenter.name
      } : null;
    })
    .filter((presenter): presenter is { id: string; name: string } => presenter !== null);
}

/**
 * Count ideas by status for a session
 * @param ctx - Database context
 * @param sessionId - Session ID to count ideas for
 * @returns Object with count statistics
 */
export async function getIdeaStatsBySession(
  ctx: QueryCtx,
  sessionId: Id<"sessions">
): Promise<{
  total: number;
  submitted: number;
  draft: number;
}> {
  const ideas = await getIdeasBySession(ctx, sessionId);
  
  const submitted = ideas.filter(idea => idea.submitted).length;
  const draft = ideas.length - submitted;
  
  return {
    total: ideas.length,
    submitted,
    draft
  };
}

/**
 * Get ideas with vote counts for analytics
 * @param ctx - Database context
 * @param eventId - Event ID to get ideas for
 * @returns Array of ideas with vote statistics
 */
export async function getIdeasWithVoteStats(
  ctx: QueryCtx,
  eventId: Id<"events">
): Promise<any[]> {
  const ideas = await getIdeasByEvent(ctx, eventId);
  
  return await Promise.all(
    ideas.map(async (idea) => {
      const votes = await ctx.db
        .query("votes")
        .withIndex("by_idea", (q) => q.eq("ideaId", idea._id))
        .collect();
      
      return {
        ...idea,
        voteCount: votes.length,
        totalScore: votes.reduce((sum, vote) => sum + vote.score, 0),
        averageScore: votes.length > 0 ? votes.reduce((sum, vote) => sum + vote.score, 0) / votes.length : 0
      };
    })
  );
}

// ===== MUTATION HELPER FUNCTIONS =====

/**
 * Create a new idea
 * @param ctx - Database context
 * @param args - Idea data for creation
 * @returns Idea ID
 */
export async function createIdea(
  ctx: any,
  args: {
    name: string;
    description?: string;
    presenters: Array<{ id: string; name: string; }>;
    userId: Id<"users">;
    sessionId: Id<"sessions">;
    teamId: Id<"teams">;
    eventId: Id<"events">;
  }
): Promise<Id<"ideas">> {
  const now = Date.now();
  return await ctx.db.insert("ideas", {
    name: args.name,
    description: args.description,
    presenters: args.presenters,
    userId: args.userId,
    sessionId: args.sessionId,
    teamId: args.teamId,
    eventId: args.eventId,
    submitted: false,
    createdAt: now,
    updatedAt: now,
  });
}

/**
 * Update an existing idea
 * @param ctx - Database context
 * @param ideaId - Idea ID to update
 * @param args - Update data
 * @returns void
 */
export async function updateIdea(
  ctx: any,
  ideaId: Id<"ideas">,
  args: {
    name?: string;
    description?: string;
    presenters?: Array<{ id: string; name: string; }>;
  }
): Promise<void> {
  const updateData: any = {
    updatedAt: Date.now(),
  };
  
  if (args.name !== undefined) updateData.name = args.name;
  if (args.description !== undefined) updateData.description = args.description;
  if (args.presenters !== undefined) updateData.presenters = args.presenters;
  
  await ctx.db.patch(ideaId, updateData);
}

/**
 * Delete an idea
 * @param ctx - Database context
 * @param ideaId - Idea ID to delete
 * @returns void
 */
export async function deleteIdea(
  ctx: any,
  ideaId: Id<"ideas">
): Promise<void> {
  await ctx.db.delete(ideaId);
}

/**
 * Update idea submission status
 * @param ctx - Database context
 * @param ideaId - Idea ID to update
 * @param submitted - Submission status
 * @returns void
 */
export async function updateIdeaSubmissionStatus(
  ctx: any,
  ideaId: Id<"ideas">,
  submitted: boolean
): Promise<void> {
  const now = Date.now();
  const updateData: any = {
    submitted,
    updatedAt: now,
  };
  
  if (submitted) {
    updateData.submittedAt = now;
  } else {
    updateData.submittedAt = undefined;
  }
  
  await ctx.db.patch(ideaId, updateData);
}