import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getActiveEvent, getActiveEventOrNull } from "./lib/eventHelpers";
import { getUserByUsername, getUserByIdOrThrow } from "./lib/userHelpers";
import { getActiveSessionForActiveEvent, getSessionById, getSessionByIdOrThrow, getSessionsByEvent, updateSession } from "./lib/sessionHelpers";
import { getTeamsByEvent } from "./lib/teamHelpers";
import { 
  getIdeaByIdOrThrow,
  getIdeasByTeamAndSession as getIdeasByTeamAndSessionHelper,
  getIdeasByUserAndSession as getIdeasByUserAndSessionHelper,
  getIdeasByCurrentUser as getIdeasByCurrentUserHelper,
  userHasIdeasForActiveSession,
  getPresenterDetails,
  getTeamMembers as getTeamMembersHelper,
  getIdeasGroupedByTeam,
  createI<PERSON> as createIdeaHelper,
  updateIdea as updateIdeaHelper,
  deleteIdea as deleteIdeaHelper
} from "./lib/ideaHelpers";
import { batchLoadUsers, batchLoadSessions, batchLoadSparks, batchUpdateEntities } from "./lib/batchHelpers";
import { 
  CreateResponse, 
  UpdateResponse, 
  DeleteResponse, 
  MutationResponse,
  ErrorCode 
} from "./lib/responseTypes";
import { 
  createCreateResponse, 
  createUpdateResponse, 
  createDeleteResponse, 
  createSuccessResponse,
  throwConvexError 
} from "./lib/responseUtils";

export const createIdea = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    presenters: v.array(v.id("users")), // Array of user IDs
    userId: v.id("users"),
    sessionId: v.id("sessions"),
    teamId: v.id("teams"),
  },
  handler: async (ctx, args): Promise<CreateResponse<"ideas">> => {
    // Validate required fields
    if (!args.name || args.name.trim() === "") {
      throwConvexError("Idea name is required", ErrorCode.REQUIRED_FIELD_MISSING);
    }

    // Get session to find eventId
    const session = await getSessionByIdOrThrow(ctx, args.sessionId);
    if (!session) {
      throwConvexError("Session not found", ErrorCode.RESOURCE_NOT_FOUND);
    }

    // Get presenter details using standardized helper
    const presenterDetails = await getPresenterDetails(ctx, args.presenters);

    // Use helper function instead of direct database operation
    const ideaId = await createIdeaHelper(ctx, {
      name: args.name,
      description: args.description,
      presenters: presenterDetails,
      userId: args.userId,
      sessionId: args.sessionId,
      teamId: args.teamId,
      eventId: session.eventId,
    });

    return createCreateResponse(ideaId);
  },
});

export const getIdeasByTeamAndSession = query({
  args: { teamId: v.id("teams"), sessionId: v.id("sessions") },
  handler: async (ctx, args) => {
    return await getIdeasByTeamAndSessionHelper(ctx, args.teamId, args.sessionId);
  },
});

export const getIdeasByCurrentUser = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    return await getIdeasByCurrentUserHelper(ctx, args.userId);
  },
});

// Super optimized query - uses .take(1) to get both hasIdeas and hasSubmitted
export const getUserIdeasSummary = query({
  args: { 
    userId: v.id("users"),
    sessionId: v.id("sessions")
  },
  handler: async (ctx, args) => {
    // Get the session once instead of fetching inside helper
    const activeSession = await ctx.db.get(args.sessionId);
    return await userHasIdeasForActiveSession(ctx, args.userId, activeSession);
  },
});

export const updateIdea = mutation({
  args: {
    ideaId: v.id("ideas"),
    name: v.string(),
    description: v.optional(v.string()),
    presenters: v.array(v.string()), // Array of user IDs
  },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    // Validate required fields
    if (!args.name || args.name.trim() === "") {
      throwConvexError("Idea name is required", ErrorCode.REQUIRED_FIELD_MISSING);
    }

    // Verify idea exists
    const existingIdea = await getIdeaByIdOrThrow(ctx, args.ideaId);
    if (!existingIdea) {
      throwConvexError("Idea not found", ErrorCode.RESOURCE_NOT_FOUND);
    }

    // Get presenter details using standardized helper
    const presenterDetails = await getPresenterDetails(ctx, args.presenters as any);

    // Use helper function instead of direct database operation
    await updateIdeaHelper(ctx, args.ideaId, {
      name: args.name,
      description: args.description,
      presenters: presenterDetails,
    });

    return createUpdateResponse();
  },
});

export const deleteIdea = mutation({
  args: { ideaId: v.id("ideas") },
  handler: async (ctx, args): Promise<DeleteResponse> => {
    // Verify idea exists
    const existingIdea = await getIdeaByIdOrThrow(ctx, args.ideaId);
    if (!existingIdea) {
      throwConvexError("Idea not found", ErrorCode.RESOURCE_NOT_FOUND);
    }

    // Use helper function instead of direct database operation
    await deleteIdeaHelper(ctx, args.ideaId);
    return createDeleteResponse();
  },
});

export const submitTeamIdeas = mutation({
  args: { 
    userId: v.id("users"),
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args): Promise<MutationResponse<{
    isSessionComplete: boolean;
    submittedIdeasCount: number;
  }>> => {
    // Get user data to find team
    const user = await getUserByIdOrThrow(ctx, args.userId);
    if (!user) {
      throwConvexError("User not found", ErrorCode.RESOURCE_NOT_FOUND);
    }

    // Get active event
    const activeEvent = await getActiveEvent(ctx);

    // Find user's team for the active event
    const userEventData = user.events?.find((e: any) => e.eventId === activeEvent._id);
    if (!userEventData?.teamId) {
      throwConvexError("User not assigned to a team", ErrorCode.OPERATION_NOT_ALLOWED);
    }

    // Mark only this user's ideas as submitted
    const userIdeas = await getIdeasByUserAndSessionHelper(ctx, args.userId, args.sessionId);

    const now = Date.now();
    // Batch update all user ideas to avoid N+1 query pattern
    const ideaUpdates = userIdeas.map(idea => ({
      id: idea._id,
      updateData: {
        submitted: true,
        submittedAt: now,
        updatedAt: now,
      }
    }));
    await batchUpdateEntities(ctx, ideaUpdates);

    // Get session and update finishedTeams
    const session = await getSessionByIdOrThrow(ctx, args.sessionId);
    if (!session) {
      throwConvexError("Session not found", ErrorCode.RESOURCE_NOT_FOUND);
    }

    // Get all ideas for this team and session
    const allTeamIdeas = await getIdeasByTeamAndSessionHelper(ctx, userEventData.teamId, args.sessionId);
    
    // Check if all ideas for this team have been submitted
    const allTeamIdeasSubmitted = allTeamIdeas.length > 0 && allTeamIdeas.every(idea => idea.submitted === true);
    
    const finishedTeams = session.finishedTeams || [];
    const teamIdString = userEventData.teamId.toString();
    
    // Only add team to finishedTeams if ALL team ideas have been submitted AND team is not already marked as finished
    if (allTeamIdeasSubmitted && !finishedTeams.includes(teamIdString)) {
      // Use helper function instead of direct database operation
      await updateSession(ctx, args.sessionId, {
        finishedTeams: [...finishedTeams, teamIdString],
      });
    }

    // Check if all teams have finished
    const allTeams = await getTeamsByEvent(ctx, activeEvent._id);
    const updatedFinishedTeams = allTeamIdeasSubmitted && !finishedTeams.includes(teamIdString) 
      ? [...finishedTeams, teamIdString] 
      : finishedTeams;

    const isSessionComplete = updatedFinishedTeams.length >= allTeams.length;

    return createSuccessResponse({
      isSessionComplete,
      submittedIdeasCount: userIdeas.length,
    });
  },
});

export const getTeamMembers = query({
  args: { teamId: v.id("teams") },
  handler: async (ctx, args) => {
    return await getTeamMembersHelper(ctx, args.teamId);
  },
});

// Admin functions for IdeasManagement
export const getAllIdeasGroupedByTeam = query({
  args: { sessionId: v.optional(v.id("sessions")) },
  handler: async (ctx, args) => {
    return await getIdeasGroupedByTeam(ctx, args.sessionId);
  },
});


// Helper function to fetch all required data for team grouping
async function fetchIdeasData(ctx: any, activeEvent: any, sessionId?: string) {
  // Get all teams for active event
  const teams = await getTeamsByEvent(ctx, activeEvent._id);

  // Get all data types - filter by session if specified, otherwise get all from active event
  let ideas, sparkSubmissions;
  
  if (sessionId) {
    // Specific session requested
    [ideas, sparkSubmissions] = await Promise.all([
      ctx.db.query("ideas")
        .withIndex("by_session", (q: any) => q.eq("sessionId", sessionId))
        .take(500),
      ctx.db.query("sparkSubmissions")
        .withIndex("by_session", (q: any) => q.eq("sessionId", sessionId))
        .take(500)
    ]);
  } else {
    // All sessions - get all data from active event excluding Quickfire sessions
    const allSessions = await getSessionsByEvent(ctx, activeEvent._id);
    const nonQuickfireSessions = allSessions.filter((session: any) => session.type !== 'Quickfire');
    
    const sessionIds = nonQuickfireSessions.map((s: any) => s._id);
    
    // Use batch queries to load ideas directly for specific sessions instead of loading all and filtering
    const ideasPromises = sessionIds.map((sessionId: string) => 
      ctx.db.query("ideas")
        .withIndex("by_session", (q: any) => q.eq("sessionId", sessionId))
        .take(500)
    );
    
    const sparkSubmissionsPromises = sessionIds.map((sessionId: string) => 
      ctx.db.query("sparkSubmissions")
        .withIndex("by_session", (q: any) => q.eq("sessionId", sessionId))
        .take(500)
    );
    
    const [ideasArrays, sparkSubmissionsArrays] = await Promise.all([
      Promise.all(ideasPromises),
      Promise.all(sparkSubmissionsPromises)
    ]);
    
    // Flatten the arrays
    ideas = ideasArrays.flat();
    sparkSubmissions = sparkSubmissionsArrays.flat();
  }

  return { teams, ideas, sparkSubmissions };
}

// Helper function to process and enhance ideas and spark submissions data
async function processIdeasData(ctx: any, ideas: any[], sparkSubmissions: any[]) {
  // Batch lookup all required data to avoid N+1 patterns
  const allUserIds = Array.from(new Set([
    ...ideas.map(idea => idea.userId),
    ...sparkSubmissions.map(spark => spark.userId)
  ]));
  const allSessionIds = Array.from(new Set([
    ...ideas.map(idea => idea.sessionId),
    ...sparkSubmissions.map(spark => spark.sessionId).filter((id): id is NonNullable<typeof id> => id !== null && id !== undefined)
  ]));
  const allSparkIds = Array.from(new Set(sparkSubmissions.map(spark => spark.sparkId)));

  const [usersData, sessionsData, sparksData] = await Promise.all([
    batchLoadUsers(ctx, allUserIds),
    batchLoadSessions(ctx, allSessionIds),
    batchLoadSparks(ctx, allSparkIds)
  ]);

  // Use batch loaded maps directly
  const usersMap = usersData;
  const sessionsMap = sessionsData;
  const sparksMap = sparksData;

  return { usersMap, sessionsMap, sparksMap };
}

// Helper function to group ideas by team
function groupIdeasByTeam(
  ideas: any[],
  sparkSubmissions: any[],
  teams: any[],
  activeEvent: any,
  usersMap: Map<string, any>,
  sessionsMap: Map<string, any>,
  sparksMap: Map<string, any>
) {
  const groupedData: any = {};
  
  // Process ideas
  for (const idea of ideas) {
    const team = teams.find(t => t._id === idea.teamId);
    if (!team) continue;

    const user = usersMap.get(idea.userId);
    const session = sessionsMap.get(idea.sessionId);
    
    if (!groupedData[idea.teamId]) {
      groupedData[idea.teamId] = {
        teamName: team.name,
        ideas: [],
        sparkSubmissions: [],
        lastUpdate: 0
      };
    }

    const ideaWithDetails = {
      _id: idea._id,
      name: idea.name,
      description: idea.description || "",
      eventId: activeEvent._id,
      eventName: activeEvent.name,
      sessionId: idea.sessionId,
      sessionName: session?.name || "Unknown Session",
      userId: idea.userId,
      teamId: idea.teamId,
      teamLeadId: idea.userId, // For compatibility
      createdAt: idea.createdAt,
      updatedAt: idea.updatedAt,
      submittedAt: idea.submittedAt,
      submitted: idea.submitted || false,
      userName: user?.name || "Unknown",
      teamName: team.name,
      presenters: idea.presenters || []
    };

    groupedData[idea.teamId].ideas.push(ideaWithDetails);
    groupedData[idea.teamId].lastUpdate = Math.max(
      groupedData[idea.teamId].lastUpdate, 
      idea.updatedAt || idea.submittedAt || idea.createdAt
    );
  }

  // Process SPARK SUBMISSIONS
  for (const sparkSubmission of sparkSubmissions) {
    const team = teams.find(t => t._id === sparkSubmission.teamId);
    if (!team) continue;

    const user = usersMap.get(sparkSubmission.userId);
    const session = sparkSubmission.sessionId ? sessionsMap.get(sparkSubmission.sessionId) : null;
    const spark = sparksMap.get(sparkSubmission.sparkId);
    
    if (!groupedData[sparkSubmission.teamId]) {
      groupedData[sparkSubmission.teamId] = {
        teamName: team.name,
        ideas: [],
        sparkSubmissions: [],
        lastUpdate: 0
      };
    }

    const sparkSubmissionWithDetails = {
      _id: sparkSubmission._id,
      sessionId: sparkSubmission.sessionId,
      sessionName: session?.name || "Unknown Session",
      userId: sparkSubmission.userId,
      userName: user?.name || "Unknown",
      teamId: sparkSubmission.teamId,
      teamName: team.name,
      sparkId: sparkSubmission.sparkId,
      sparkName: spark?.name || "Unknown Spark",
      sparkColor: spark?.color || "#b96eff",
      eventId: activeEvent._id,
      eventName: activeEvent.name,
      data: sparkSubmission.data || {},
      submittedAt: sparkSubmission.submittedAt,
      updatedAt: sparkSubmission.updatedAt,
      // Include spark field configuration for rendering
      sparkFields: spark?.fields || []
    };

    groupedData[sparkSubmission.teamId].sparkSubmissions.push(sparkSubmissionWithDetails);
    groupedData[sparkSubmission.teamId].lastUpdate = Math.max(
      groupedData[sparkSubmission.teamId].lastUpdate, 
      sparkSubmission.updatedAt || sparkSubmission.submittedAt
    );
  }

  // Sort ideas and spark submissions within each team group (newest first)
  Object.values(groupedData).forEach((teamGroup: any) => {
    // Sort ideas by updatedAt descending (newest first)
    if (teamGroup.ideas) {
      teamGroup.ideas.sort((a: any, b: any) => {
        const aTime = a.updatedAt || a.createdAt;
        const bTime = b.updatedAt || b.createdAt;
        return new Date(bTime).getTime() - new Date(aTime).getTime();
      });
    }
    
    // Sort spark submissions by updatedAt descending (newest first)
    if (teamGroup.sparkSubmissions) {
      teamGroup.sparkSubmissions.sort((a: any, b: any) => {
        const aTime = a.updatedAt || a.submittedAt;
        const bTime = b.updatedAt || b.submittedAt;
        return new Date(bTime).getTime() - new Date(aTime).getTime();
      });
    }
  });

  return groupedData;
}

// Combined query for IdeasManagement - gets all data types grouped by team
export const getAllDataGroupedByTeam = query({
  args: { sessionId: v.optional(v.id("sessions")) },
  handler: async (ctx, args) => {
    // Get active event
    const activeEvent = await getActiveEventOrNull(ctx);

    if (!activeEvent) {
      return []; // Return empty array if no active event (data cleared)
    }

    // Fetch all required data
    const { teams, ideas, sparkSubmissions } = await fetchIdeasData(ctx, activeEvent, args.sessionId);

    // Process and enhance data with lookups
    const { usersMap, sessionsMap, sparksMap } = await processIdeasData(ctx, ideas, sparkSubmissions);

    // Group data by team
    return groupIdeasByTeam(ideas, sparkSubmissions, teams, activeEvent, usersMap, sessionsMap, sparksMap);
  },
});

// Withdraw team IDEAS (ONLY IDEAS GET SUBMITTED/WITHDRAWN)
export const withdrawAllTeamData = mutation({
  args: {
    teamId: v.id("teams"),
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args): Promise<MutationResponse<{
    withdrawnIdeas: number;
  }>> => {
    // Get ideas for this team and session - withdrawal only affects IDEAS, not spark submissions
    const ideas = await getIdeasByTeamAndSessionHelper(ctx, args.teamId, args.sessionId);

    // Batch withdraw ideas (mark as not submitted so they can edit again) to avoid N+1 query pattern
    const ideaUpdates = ideas.map(idea => ({
      id: idea._id,
      updateData: {
        submitted: false,
        submittedAt: undefined,
        updatedAt: Date.now()
      }
    }));
    await batchUpdateEntities(ctx, ideaUpdates);

    // Update session to remove team from finishedTeams 
    const session = await getSessionById(ctx, args.sessionId);
    if (session && session.finishedTeams) {
      const updatedFinishedTeams = session.finishedTeams.filter(
        (teamId: string) => teamId !== args.teamId.toString()
      );
      
      // Use helper function instead of direct database operation
      await updateSession(ctx, args.sessionId, {
        finishedTeams: updatedFinishedTeams,
      });
    }

    return createSuccessResponse({
      withdrawnIdeas: ideas.length
    });
  },
});

// Withdraw specific team lead IDEAS (ONLY SPECIFIC TEAM LEAD'S IDEAS GET WITHDRAWN)
export const withdrawTeamLeadIdeas = mutation({
  args: {
    teamId: v.id("teams"),
    sessionId: v.id("sessions"),
    userId: v.id("users"), // specific team lead
  },
  handler: async (ctx, args): Promise<MutationResponse<{
    withdrawnIdeas: number;
  }>> => {
    // Get ideas for this specific team lead and session - withdrawal only affects this team lead's IDEAS
    const ideas = await getIdeasByUserAndSessionHelper(ctx, args.userId, args.sessionId);

    // Batch withdraw ideas (mark as not submitted so they can edit again) to avoid N+1 query pattern
    const ideaUpdates = ideas.map(idea => ({
      id: idea._id,
      updateData: {
        submitted: false,
        submittedAt: undefined,
        updatedAt: Date.now()
      }
    }));
    await batchUpdateEntities(ctx, ideaUpdates);

    // ALWAYS remove team from finishedTeams when ANY team lead withdraws (team is only finished when ALL team leads submit ALL ideas)
    const session = await getSessionById(ctx, args.sessionId);
    if (session && session.finishedTeams) {
      const updatedFinishedTeams = session.finishedTeams.filter(
        (teamId: string) => teamId !== args.teamId.toString()
      );
      
      // Use helper function instead of direct database operation
      await updateSession(ctx, args.sessionId, {
        finishedTeams: updatedFinishedTeams,
      });
    }

    return createSuccessResponse({
      withdrawnIdeas: ideas.length
    });
  },
});

// Phase 2 Optimization: Combined query function for Ideas page
export const getIdeasPageData = query({
  args: { username: v.string() },
  handler: async (ctx, args) => {
    // Parallel queries for all needed data
    const [user, activeEvent, activeSession] = await Promise.all([
      getUserByUsername(ctx, args.username),
      getActiveEventOrNull(ctx),
      getActiveSessionForActiveEvent(ctx)
    ]);

    if (!user || !activeEvent || !activeSession) {
      return {
        activeEvent: activeEvent || null,
        activeSession: activeSession || null,
        currentUser: user || null
      };
    }

    return {
      activeEvent,
      activeSession,
      currentUser: user
    };
  },
});

// Phase 2 Optimization: Combined query function for IdeaForm
export const getIdeaFormData = query({
  args: { username: v.string() },
  handler: async (ctx, args) => {
    // Parallel queries for all form data
    const [user, activeEvent, activeSession] = await Promise.all([
      getUserByUsername(ctx, args.username),
      getActiveEventOrNull(ctx),
      getActiveSessionForActiveEvent(ctx)
    ]);

    if (!user || !activeEvent || !activeSession) {
      return {
        activeEvent: activeEvent || null,
        activeSession: activeSession || null,
        currentUser: user || null,
        ideas: [],
        teamMembers: []
      };
    }

    // Find user's team for the active event
    const userEventData = user.events?.find((e: any) => e.eventId === activeEvent._id);
    const teamId = userEventData?.teamId;

    // Get user's ideas and team members in parallel
    const [ideas, teamMembers] = await Promise.all([
      // Get ideas for this specific user and session
      getIdeasByUserAndSessionHelper(ctx, user._id, activeSession._id),
      teamId ? getTeamMembersHelper(ctx, teamId) : Promise.resolve([])
    ]);

    return {
      activeEvent,
      activeSession,
      currentUser: user,
      ideas,
      teamMembers
    };
  },
});