"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ConvexError } from 'convex/values';
import { useAction } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Input } from '@/components/ui/input';
import { PasswordInput } from '@/components/ui/password-input';
import { Button } from '@/components/ui/button';
import { Label } from '../ui/label';

interface FormState {
    username: string;
    name: string;
    password: string;
    confirmPassword: string;
}

interface RegisterFormProps {
    eventName: string;
}

export default function RegisterForm({ eventName }: RegisterFormProps) {
    const router = useRouter();
    const registerUser = useAction(api.users.registerUser);
    const [formState, setFormState] = useState<FormState>({
        username: '',
        name: '',
        password: '',
        confirmPassword: ''
    });
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<string | null>(null);
    const [loading, setLoading] = useState(false);

    const handleRegister = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!formState.username || !formState.name || !formState.password || !formState.confirmPassword) {
            setError('Please fill in all fields');
            return;
        }

        // Validate username format
        if (formState.username.includes(' ')) {
            setError('Username cannot contain spaces');
            return;
        }

        if (formState.username !== formState.username.toLowerCase()) {
            setError('Username must be lowercase');
            return;
        }

        if (formState.password !== formState.confirmPassword) {
            setError('Passwords do not match');
            return;
        }

        setLoading(true);
        setError(null);

        try {
            const data = await registerUser({
                username: formState.username,
                name: formState.name,
                password: formState.password,
            });

            // Show appropriate success message based on whether it's a new or existing user
            if (data.data!.user.autoApproved) {
                if (data.data!.user.existingUser) {
                    setSuccess(`Welcome back! You've been registered for ${eventName}. You can now log in.`);
                } else {
                    setSuccess(`Registration successful for ${eventName}! You can now log in.`);
                }
            } else {
                if (data.data!.user.existingUser) {
                    setSuccess(`Welcome back! Your registration for ${eventName} is pending approval.`);
                } else {
                    setSuccess(`Registration successful for ${eventName}! Please wait for administrator approval.`);
                }
            }
            setError(null);
            
            // Clear form
            setFormState({
                username: '',
                name: '',
                password: '',
                confirmPassword: ''
            });

            // If auto-approved, redirect to login after showing message
            if (data.data!.user.autoApproved) {
                setTimeout(() => {
                    router.push('/login');
                }, 2500); // Show message for 2.5 seconds
            }
        } catch (error) {
            if (error instanceof ConvexError) {
                const errorData = error.data as { message: string };
                setError(errorData.message);
            } else {
                setError(error instanceof Error ? error.message : 'Registration failed');
            }
        } finally {
            setLoading(false);
        }
    };

    return (
        <form onSubmit={handleRegister} className="space-y-5">
            <div className="space-y-4">
                <div className="space-y-2">
                    <Label htmlFor="name">
                        Name
                    </Label>
                    <Input
                        id="name"
                        type="text"
                        value={formState.name}
                        onChange={(e) => setFormState(prev => ({ ...prev, name: e.target.value }))}
                        disabled={loading}
                        className="input-standard"
                    />
                </div>
                
                <div className="space-y-2">
                    <Label htmlFor="username">
                        Username
                    </Label>
                    <Input
                        id="username"
                        type="text"
                        value={formState.username}
                        onChange={(e) => {
                            const sanitizedUsername = e.target.value.toLowerCase().replace(/\s+/g, '');
                            setFormState(prev => ({ ...prev, username: sanitizedUsername }));
                        }}
                        disabled={loading}
                        className="input-standard"
                    />
                </div>
                
                <div className="space-y-2">
                    <Label htmlFor="password">
                        Password
                    </Label>
                    <PasswordInput
                        id="password"
                        value={formState.password}
                        onChange={(e) => setFormState(prev => ({ ...prev, password: e.target.value }))}
                        disabled={loading}
                        className="input-standard"
                    />
                </div>
                
                <div className="space-y-2">
                    <Label htmlFor="confirmPassword">
                        Confirm Password
                    </Label>
                    <PasswordInput
                        id="confirmPassword"
                        value={formState.confirmPassword}
                        onChange={(e) => setFormState(prev => ({ ...prev, confirmPassword: e.target.value }))}
                        disabled={loading}
                        className="input-standard"
                    />
                </div>
            </div>
            
            {error && (
                <div className="text-sm bg-primary/10 border border-primary/20 text-primary p-3">
                    {error}
                </div>
            )}
            
            {success && (
                <div className="text-sm bg-secondary/10 border border-secondary/20 text-secondary p-3">
                    {success}
                </div>
            )}
            
            <Button
                type="submit"
                disabled={loading}
                className="w-full"
            >
                {loading ? "Registering..." : "REGISTER"}
            </Button>
        </form>
    );
}