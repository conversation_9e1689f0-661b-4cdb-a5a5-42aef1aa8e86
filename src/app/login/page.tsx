"use client";

import Link from 'next/link';
import { useState, useEffect, Suspense } from 'react';
import { ConvexError } from 'convex/values';
import { useRouter } from 'next/navigation';
import { signIn, useSession } from 'next-auth/react';
import { useQuery } from "convex/react";
import { ConvexHttpClient } from "convex/browser";
import { api } from "@/../convex/_generated/api";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { PasswordInput } from '@/components/ui/password-input';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import LoginLogo from '@/components/icons/LoginLogo';

// Initialize Convex client for imperative queries
const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

interface FormState {
    username: string;
    password: string;
}


export default function LoginPage() {
    const router = useRouter();
    const { data: session, status } = useSession();
    const activeEvent = useQuery(api.events.getActiveEvent);
    
    const [formState, setFormState] = useState<FormState>({
        username: '',
        password: ''
    });

    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState(false);

    // Handle session-based redirects
    useEffect(() => {
        if (status === 'authenticated' && session?.user) {
            setError(null);
            
            if (session.user.role === 'admin') {
                router.push('/admin');
            } else if (session.user.status === 'approved') {
                router.push('/user');
            } else {
                setError('Your account is pending approval');
                signIn('credentials', { redirect: false });
            }
        }
    }, [status, session, router]);


    const handleLogin = async (e?: React.FormEvent) => {
        if (e) {
            e.preventDefault();
        }

        if (!formState.username || !formState.password) {
            setError('Please fill in all fields');
            return;
        }

        setLoading(true);
        setError(null);

        try {
            // First check if the user is an admin
            const isAdminData = await convex.query(api.users.checkUserAdmin, { 
                username: formState.username 
            });
            const isAdmin = isAdminData.isAdmin;

            // If not admin, check for active events
            if (!isAdmin) {
                if (!activeEvent) {
                    setError('No active events for now! Please try again later.');
                    setLoading(false);
                    return;
                }
            }

            const result = await signIn('credentials', {
                username: formState.username,
                password: formState.password,
                redirect: false
            });

            if (result?.error) {
                // Handle specific error messages
                if (result.error === 'Your account is pending approval') {
                    setError('Your account is pending administrator approval');
                } else if (result.error === 'Your registration was not approved') {
                    setError('Your registration was not approved. Please contact an administrator.');
                } else if (result.error === 'Your account is not registered for the current event') {
                    setError(`Your account is not registered for the current event: ${activeEvent?.name}`);
                } else if (result.error === 'No active events for now!') {
                    setError('No active events for now! Please try again later.');
                } else {
                    setError(result.error);
                }
            }
        } catch (error) {
            if (error instanceof ConvexError) {
                const errorData = error.data as { message: string };
                setError(errorData.message);
            } else {
                setError(error instanceof Error ? error.message : 'Login failed');
            }
        } finally {
            setLoading(false);
        }
    };

    // Don't render the form if we're already authenticated
    if (status === 'authenticated' && !error) {
        return null;
    }

    return (
        <div className="relative w-screen min-h-screen flex justify-center items-center bg-background p-8 md:p-4 theme-background">
            

            {/* Main Content */}
            <div className="relative z-10 w-full max-w-md">
                <Suspense fallback={
                    <Card className="bg-card/90 backdrop-blur-sm border-accent">
                        <CardContent className="flex items-center justify-center py-12">
                            <div className="text-muted-foreground">Loading...</div>
                        </CardContent>
                    </Card>
                }>
                    <Card className="bg-card/90 backdrop-blur-sm border-accent">
                        <CardHeader className="flex flex-col items-center space-y-4 pb-0">
                            {/* Logo Container - Keep same logic */}
                            <LoginLogo className="text-secondary" size={100} />
                            <div className="text-card-foreground text-center">
                                <div className="text-sm font-mono">
                                    Welcome to
                                </div>
                                <h1 className="text-2xl md:text-3xl font-ultrabold">
                                    {activeEvent ? activeEvent.name : 'Lion X'}
                                </h1>
                            </div>
                        </CardHeader>
                        
                        <CardContent className="space-y-6">
                            <form onSubmit={handleLogin} className="space-y-5">
                                <div className="space-y-4">
                                    <Input
                                        type="text"
                                        placeholder="Username"
                                        value={formState.username}
                                        onChange={(e) => {
                                            const sanitizedUsername = e.target.value.toLowerCase().replace(/\s+/g, '');
                                            setFormState(prev => ({ ...prev, username: sanitizedUsername }));
                                        }}
                                        disabled={loading}
                                        className="input-standard"
                                    />
                                    <PasswordInput
                                        placeholder="••••••••"
                                        value={formState.password}
                                        onChange={(e) => setFormState(prev => ({ ...prev, password: e.target.value }))}
                                        disabled={loading}
                                        className="input-standard"
                                    />
                                </div>
                                
                                {error && (
                                    <div className="text-sm bg-primary/10 border border-primary/20 text-primary p-3">
                                        {error}
                                    </div>
                                )}
                                
                                <div className={`flex flex-col sm:flex-row gap-4 ${activeEvent ? 'sm:justify-between' : 'justify-center'}`}>
                                    <Button
                                        type="submit"
                                        disabled={loading}
                                        className="flex-1"
                                    >
                                        {loading ? "Logging in..." : "LOGIN"}
                                    </Button>
                                    {activeEvent && (
                                        <Button asChild variant="outline" className="flex-1">
                                            <Link href="/register">
                                                SIGN UP
                                            </Link>
                                        </Button>
                                    )}
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </Suspense>
            </div>
        </div>
    );
}
