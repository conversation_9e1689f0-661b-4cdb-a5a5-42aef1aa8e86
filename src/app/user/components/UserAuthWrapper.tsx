"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { signOut, useSession } from 'next-auth/react';
import { useQuery } from "convex/react";
import { api } from "@/../convex/_generated/api";

interface UserAuthWrapperProps {
  children: React.ReactNode;
}

export default function UserAuthWrapper({ children }: UserAuthWrapperProps) {
  const router = useRouter();
  const { data: sessionData, status } = useSession();
  
  // Real-time Convex queries for authentication checks
  const activeEvent = useQuery(api.events.getActiveEvent);
  const currentUser = useQuery(
    api.users.getCurrentUser, 
    sessionData?.user?.username ? {
      username: sessionData.user.username,
      activeEventId: activeEvent?._id
    } : "skip"
  );
  
  // Check user approval status
  const userApprovalStatus = useQuery(
    api.users.getUserApprovalStatus,
    sessionData?.user?.username ? {
      username: sessionData.user.username
    } : "skip"
  );

  // Handle authentication and role-based access
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
      return;
    }

    if (status === 'authenticated' && sessionData?.user?.role === 'admin') {
      router.push('/admin');
      return;
    }
  }, [status, sessionData, router]);

  // Update local user state when Convex data changes
  useEffect(() => {
    if (currentUser === null && sessionData?.user?.username) {
      // User was deleted from database but session still exists
      signOut({ callbackUrl: '/login' });
      return;
    }
  }, [currentUser, sessionData?.user?.username]);

  // Check approval status and logout if not approved
  useEffect(() => {
    if (userApprovalStatus && userApprovalStatus.status !== 'approved') {
      console.log('User approval status changed to:', userApprovalStatus.status);
      signOut({ callbackUrl: '/login' });
    }
  }, [userApprovalStatus]);

  // Check event status and logout if event is deactivated
  useEffect(() => {
    if (activeEvent === null && sessionData?.user?.role !== 'admin') {
      console.log('Event deactivated, logging out user');
      signOut({ callbackUrl: '/login' });
    }
  }, [activeEvent, sessionData]);

  // Don't render anything while checking authentication
  if (status === 'loading' || !sessionData) {
    return null;
  }

  // Show loading if authentication queries are still loading
  const isLoadingAuth = (
    activeEvent === undefined ||
    currentUser === undefined ||
    userApprovalStatus === undefined
  );

  if (isLoadingAuth) {
    return (
      <div className="min-h-screen text-white font-mono flex flex-col relative theme-background">
      </div>
    );
  }

  return <>{children}</>;
}