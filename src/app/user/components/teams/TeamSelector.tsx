"use client";

import { useState } from 'react';
import { useQuery } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { StateCard } from '@/components/ui/state-card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';


interface TeamSelectorProps {
    onTeamSelected: (teamId: string, teamName: string) => void;
}

export default function TeamSelector({ onTeamSelected }: TeamSelectorProps) {
    const teams = useQuery(api.teams.getTeamsByActiveEvent);
    const [selectedTeam, setSelectedTeam] = useState<string>('');
    const [error, setError] = useState<string | null>(null);

    const loading = teams === undefined;
    const hasError = teams === null;

    const handleTeamSelect = () => {
        if (!selectedTeam) {
            setError('Please select a team');
            return;
        }

        const selectedTeamData = teams?.find(team => team._id === selectedTeam);
        if (selectedTeamData) {
            onTeamSelected(selectedTeam, selectedTeamData.name);
        }
    };

    return (
        <Dialog open={true} onOpenChange={() => {}}>
                <DialogContent showCloseButton={false} className="bg-background border-2 border-secondary rounded-none max-w-md">
                    <DialogHeader>
                        <DialogTitle className="font-ultrabold text-2xl text-primary text-center">
                            Select Your Team
                        </DialogTitle>
                        <DialogDescription className="text-sm text-muted-foreground text-center">
                            Choose your team to get started
                        </DialogDescription>
                    </DialogHeader>
                    
                    <div className="space-y-4 py-4">
                        {loading && (
                            <StateCard state="loading" title="Loading Teams..." />
                        )}
                        
                        {(hasError || error) && (
                            <StateCard 
                                state="error" 
                                title="Error" 
                                message={error || 'Failed to load teams. Please try refreshing the page.'} 
                            />
                        )}
                        
                        {teams && teams.length === 0 && (
                            <StateCard 
                                state="empty" 
                                title="No Teams Available" 
                                message="Please contact an administrator to create teams." 
                            />
                        )}
                        
                        {teams && teams.length > 0 && (
                            <>
                                <div className="space-y-3">
                                    {teams.map((team, index) => (
                                        <div
                                            key={team._id}
                                            className="animate-in fade-in slide-in-from-bottom-2 duration-300"
                                            style={{ animationDelay: `${index * 50}ms` }}
                                        >
                                            <Button
                                                variant={selectedTeam === team._id ? "default" : "outline"}
                                                className={`w-full h-12 text-lg transition-all duration-200 ${
                                                    selectedTeam === team._id 
                                                        ? 'bg-accent border-accent hover:text-accent' 
                                                        : 'bg-background border-2 border-secondary text-secondary hover:bg-secondary hover:text-secondary-foreground'
                                                }`}
                                                onClick={() => setSelectedTeam(team._id)}
                                            >
                                                {team.name}
                                            </Button>
                                        </div>
                                    ))}
                                </div>
                                
                                {selectedTeam && (
                                    <div className="pt-4 border-t border-secondary animate-in fade-in slide-in-from-bottom-2 duration-300">
                                        <Button
                                            variant="default"
                                            onClick={handleTeamSelect}
                                            className="w-full text-lg font-mono"
                                        >
                                            Confirm Selection
                                        </Button>
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                </DialogContent>
            </Dialog>
    );
}