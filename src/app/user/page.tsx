"use client";

import React, { useEffect, useState, Suspense } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useQuery } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { debug } from '@/lib/utils';
import { StateCard } from '@/components/ui/state-card';
import { UserErrorBoundary } from '@/app/user/components/UserErrorBoundary';

// Lazy load components for better performance
const Ideas = React.lazy(() => import('./components/Ideas'));
const Voting = React.lazy(() => import('./components/Voting'));
const QuickfireVoting = React.lazy(() => import('./components/QuickfireVoting'));
const SparksComponent = React.lazy(() => import('./components/SparksComponent'));

interface User {
    id: string;
    username: string;
    name: string;
    role: string;
    status?: string;
    teamId: string | null;
    teamName: string | null;
}


export default function UserPage() {
    const router = useRouter();
    const { data: sessionData, status } = useSession();
    const [user, setUser] = useState<User | null>(null);
    const [error, setError] = useState<string | null>(null);
    
    // Real-time Convex queries
    const activeEvent = useQuery(api.events.getActiveEvent);
    const activeSession = useQuery(api.sessions.getActiveSession);
    const currentUser = useQuery(
        api.users.getCurrentUser, 
        sessionData?.user?.username ? {
            username: sessionData.user.username,
            activeEventId: activeEvent?._id
        } : "skip"
    );
    
    // Optimized query - get user ideas summary instead of full ideas for conditional logic
    const userIdeasSummary = useQuery(
        api.ideas.getUserIdeasSummary,
        currentUser && activeSession ? { 
            userId: currentUser.id as Id<"users">,
            sessionId: activeSession._id
        } : "skip"
    );

    // Update local user state when Convex data changes
    useEffect(() => {
        if (currentUser) {
            setUser(currentUser);
            setError(null);
        } else if (currentUser === null && activeEvent === null) {
            setError('The system is currently unavailable as there is no active event. Please try again later.');
        }
    }, [currentUser, activeEvent]);
    
    // Handle authentication and role-based redirects
    useEffect(() => {
        if (status === 'unauthenticated') {
            router.replace('/login');
        } else if (status === 'authenticated') {
            if (sessionData?.user?.role === 'admin') {
                router.replace('/admin');
            }
        }
    }, [status, sessionData, router]);



    const renderContent = () => {
        // Show loading if Convex queries are still loading
        if (activeEvent === undefined || activeSession === undefined || currentUser === undefined) {
            return <StateCard state="loading" title="Loading..." />;
        }

        // Debug logging for session logic
        debug('[UserPage] Session Logic State:', {
            activeEvent: activeEvent?.name,
            activeSession: activeSession?.name,
            sessionType: activeSession?.type,
            user: user?.name,
            userRole: user?.role,
            userTeamId: user?.teamId,
            hasFinishedTeams: activeSession?.finishedTeams?.length || 0
        });

        if (error || !user) {
            return <StateCard state="error" title="Error" message={error || 'Failed to load user data'} />;
        }

        // Handle case where no active event exists
        if (!activeEvent) {
            return <StateCard state="info" title="No Active Event" message="There is currently no active event. Please check back later!" />;
        }

        // Handle case where user is not assigned to a team
        if (!user.teamId) {
            return <StateCard state="info" title="Team Assignment Pending" message="You haven't been assigned to a team yet. Please wait for team assignment!" />;
        }

        // Handle case where no active session exists
        if (!activeSession) {
            return <StateCard state="info" title="No session has started yet!" message="Waiting for the next session to begin..." />;
        }

        // Real-time session-based component routing
        debug('[UserPage] Routing to component based on session type:', activeSession.type);

        // Special case: Quickfire voting session (both roles vote)
        if (activeSession.type === 'Quickfire') {
            debug('[UserPage] Showing QuickfireVoting for session type: Quickfire');
            return (
                <UserErrorBoundary>
                    <Suspense fallback={<StateCard state="loading" title="Loading Quickfire Voting..." />}>
                        <QuickfireVoting key="quickfire-voting" />
                    </Suspense>
                </UserErrorBoundary>
            );
        }

        // Role-based component logic
        if (user.role === 'teamMember') {
            // Team members always see voting (unless it's a quickfire session handled above)
            debug('[UserPage] Showing Voting for teamMember role');
            return (
                <UserErrorBoundary>
                    <Suspense fallback={<StateCard state="loading" title="Loading Voting..." />}>
                        <Voting key="voting" />
                    </Suspense>
                </UserErrorBoundary>
            );
        }

        if (user.role === 'teamLead') {
            // Check if team has finished OR if current user has submitted their ideas
            const teamHasFinished = activeSession.finishedTeams?.some((teamId: string) => teamId === user.teamId) || false;
            const userHasSubmitted = userIdeasSummary?.hasSubmitted || false;
            
            debug('[UserPage] TeamLead logic:', {
                teamHasFinished,
                userHasSubmitted,
                sessionType: activeSession.type,
                finishedTeams: activeSession.finishedTeams,
                userHasIdeas: userIdeasSummary?.hasIdeas || false
            });

            // If team has finished (all team leads submitted) OR current user has submitted, show voting
            if (teamHasFinished || userHasSubmitted) {
                debug('[UserPage] Team finished or user submitted, showing Voting');
                return (
                    <UserErrorBoundary>
                        <Suspense fallback={<StateCard state="loading" title="Loading Voting..." />}>
                            <Voting key="voting" />
                        </Suspense>
                    </UserErrorBoundary>
                );
            }

            // Route to appropriate submission component based on session type
            switch (activeSession.type) {
                case 'Ideas':
                    debug('[UserPage] Showing Ideas component');
                    return (
                        <UserErrorBoundary>
                            <Suspense fallback={<StateCard state="loading" title="Loading Ideas Submission..." />}>
                                <Ideas key="ideas" />
                            </Suspense>
                        </UserErrorBoundary>
                    );
                case 'Sparks':
                    debug('[UserPage] Showing SparksComponent component');
                    return (
                        <UserErrorBoundary>
                            <Suspense fallback={<StateCard state="loading" title="Loading Sparks Form..." />}>
                                <SparksComponent key="sparks" />
                            </Suspense>
                        </UserErrorBoundary>
                    );
                default:
                    debug('[UserPage] Unknown session type, defaulting to Voting');
                    return (
                        <UserErrorBoundary>
                            <Suspense fallback={<StateCard state="loading" title="Loading Voting..." />}>
                                <Voting key="voting" />
                            </Suspense>
                        </UserErrorBoundary>
                    );
            }
        }

        // Fallback for unknown role
        debug('[UserPage] Unknown user role, defaulting to Voting');
        return (
            <UserErrorBoundary>
                <Suspense fallback={<StateCard state="loading" title="Loading Voting..." />}>
                    <Voting key="voting" />
                </Suspense>
            </UserErrorBoundary>
        );
    };

    // Don't render anything while checking authentication or if user is admin
    if (status === 'loading' || (status === 'authenticated' && sessionData?.user?.role === 'admin')) {
        return null;
    }

    return (
        <div className="relative z-10 flex-1 w-full max-w-[1200px] mx-auto responsive-user-page">
            <div className="flex justify-center items-start animate-in fade-in duration-300">
                {renderContent()}
            </div>
        </div>
    );
}
