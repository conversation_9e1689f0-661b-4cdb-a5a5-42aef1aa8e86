"use client";

import { useState } from 'react';
import { useQuery, useMutation, useAction } from "convex/react";
import { useSession } from 'next-auth/react';
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { debugError } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { StateCard } from '@/components/ui/state-card';
import AdminErrorBoundary from './AdminErrorBoundary';
import { 
    Dialog, 
    DialogContent, 
    DialogDescription,
    DialogFooter, 
    DialogHeader, 
    DialogTitle 
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { <PERSON><PERSON><PERSON>, Trash2 } from 'lucide-react';
import SparkConfigEditor from './SparkConfigEditor';
import { toast } from 'sonner';
import ColorPicker from 'react-best-gradient-color-picker';

interface Spark {
    _id: string;
    name: string;
    description?: string;
    color?: string;
    fields: Array<{
        type: "text" | "richtext" | "dropdown" | "radio" | "checkbox";
        label: string;
        placeholder?: string;
        options?: string[];
        required?: boolean;
    }>;
    createdAt: number;
    updatedAt: number;
}

function SparksManagementCore() {
    const { data: sessionData } = useSession();
    
    // Real-time queries from Convex
    const activeEvent = useQuery(api.events.getActiveEvent);
    const sparks = useQuery(api.sparks.getSparks, 
        activeEvent ? { eventId: activeEvent._id } : "skip"
    );
    
    // Mutations
    const deleteSpark = useMutation(api.sparks.deleteSpark);
    const updateSpark = useMutation(api.sparks.updateSpark);
    const checkSparkUsability = useAction(api.sparks.checkSparkUsabilityAction);
    
    // UI state
    const [showEditor, setShowEditor] = useState(false);
    const [editingSpark, setEditingSpark] = useState<Spark | null>(null);
    const [editSuccessId, setEditSuccessId] = useState<string | null>(null);
    const [isDeleting, setIsDeleting] = useState(false);
    const [showColorPicker, setShowColorPicker] = useState<string | null>(null);
    const [tempColor, setTempColor] = useState<string>("#b96eff");
    
    // Modal states
    const [modal, setModal] = useState<{
        type: 'deleteConfirm' | 'deleteError' | 'editError';
        spark: Spark | string;
        submissionCount?: number;
    } | null>(null);


    // Handle error with toast
    const handleError = (errorMessage: string) => {
        toast.error(errorMessage);
    };

    // Handle opening editor for new spark
    const handleAddSpark = () => {
        setEditingSpark(null);
        setShowEditor(true);
    };

    // Handle opening editor for existing spark
    const handleEditSpark = async (spark: Spark) => {
        try {
            // OPTIMIZED: Use consolidated usability check
            const usabilityResult = await checkSparkUsability({ sparkId: spark._id as Id<"sparks"> });
            
            if (usabilityResult.success && usabilityResult.data?.canEdit) {
                setEditingSpark(spark);
                setShowEditor(true);
            } else {
                setModal({ type: 'editError', spark: spark.name });
            }
        } catch {
            // Fallback: allow editing attempt
            setEditingSpark(spark);
            setShowEditor(true);
        }
    };

    // Handle closing editor
    const handleCloseEditor = () => {
        setShowEditor(false);
        setEditingSpark(null);
    };

    // Handle successful save
    const handleSaveSuccess = (sparkId: string) => {
        setShowEditor(false);
        setEditingSpark(null);
        setEditSuccessId(sparkId);
        
        // Clear success animation after 600ms
        setTimeout(() => {
            setEditSuccessId(null);
        }, 600);
    };

    // Handle delete confirmation  
    const handleDeleteClick = async (spark: Spark) => {
        try {
            // OPTIMIZED: Use consolidated usability check - gets all info in one call
            const usabilityResult = await checkSparkUsability({ sparkId: spark._id as Id<"sparks"> });
            
            if (usabilityResult.success && usabilityResult.data) {
                if (usabilityResult.data.canDelete) {
                    setModal({ 
                        type: 'deleteConfirm', 
                        spark, 
                        submissionCount: usabilityResult.data.submissionCount 
                    });
                } else {
                    setModal({ type: 'deleteError', spark: spark.name });
                }
            } else {
                setModal({ type: 'deleteError', spark: spark.name });
            }
        } catch {
            // Fallback: allow deletion attempt with zero submissions
            setModal({ type: 'deleteConfirm', spark, submissionCount: 0 });
        }
    };

    // Handle delete confirmation
    const handleDeleteConfirm = async () => {
        if (!modal || modal.type !== 'deleteConfirm' || isDeleting) return;

        const spark = modal.spark as Spark;
        setIsDeleting(true);

        try {
            await deleteSpark({ 
                username: sessionData?.user?.username || '',
                sparkId: spark._id as Id<"sparks"> 
            });
            setModal(null);
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to delete spark';
            toast.error(errorMessage);
            debugError('Error deleting spark:', error);
        } finally {
            setIsDeleting(false);
        }
    };

    // Handle color change
    const handleColorChange = async (spark: Spark, color: string) => {
        try {
            await updateSpark({
                username: sessionData?.user?.username || '',
                sparkId: spark._id as Id<"sparks">,
                color: color
            });
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to update color';
            toast.error(errorMessage);
            debugError('Error updating spark color:', error);
        }
    };

    // Close modal
    const closeModal = () => {
        setModal(null);
    };

    if (activeEvent === undefined) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard state="loading" title="Loading event..." />
            </div>
        );
    }

    if (!activeEvent) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard 
                    state="empty" 
                    title="No Active Event" 
                    message="Please create and activate an event first to manage sparks."
                />
            </div>
        );
    }

    if (sparks === undefined) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard state="loading" title="Loading sparks..." />
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header with Add Button */}
            <div className="flex justify-start">
                <Button 
                    onClick={handleAddSpark}
                    className="w-full"
                >
                    ADD NEW SPARK
                </Button>
            </div>

            {/* Empty State */}
            {sparks.length === 0 && (
                <div className="flex justify-center items-center min-h-[400px]">
                    <StateCard 
                        state="empty" 
                        title="No Sparks Found" 
                        message="No sparks found. Create your first spark template to get started."
                    />
                </div>
            )}
            
            {/* Sparks List */}
            {sparks.length > 0 && (
                <div className="space-y-4">
                    {sparks.map((spark: Spark) => (
                        <div
                            key={spark._id}
                            className={`admin-card ${editSuccessId === spark._id ? 'border-primary' : ''}`}
                        >
                            {/* Top Row: Spark Info */}
                            <div className="flex items-center gap-4 flex-1 min-w-0">
                                {/* Color Button */}
                                <button
                                    className="w-8 h-8 shrink-0"
                                    style={{ backgroundColor: spark.color || "#b96eff" }}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        setTempColor(spark.color || "#b96eff");
                                        setShowColorPicker(spark._id);
                                    }}
                                    title="Change spark color"
                                />

                                {/* Spark Info */}
                                <div className="flex-1 min-w-0">
                                    <div
                                        className="text-foreground hover:text-primary transition-colors cursor-pointer truncate font-medium"
                                        onClick={() => handleEditSpark(spark)}
                                    >
                                        {spark.name}
                                    </div>
                                    {spark.description && (
                                        <div className="text-muted-foreground text-sm truncate">
                                            {spark.description}
                                        </div>
                                    )}
                                    <div className="text-muted-foreground text-xs">
                                        {spark.fields.length} field{spark.fields.length !== 1 ? 's' : ''}
                                    </div>
                                </div>
                            </div>

                            {/* Action Buttons Row */}
                            <div className="flex items-center gap-3 flex-wrap sm:flex-nowrap">
                                {/* Edit Button */}
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleEditSpark(spark)}
                                    className="shrink-0 h-8 w-8 p-0 hover:!bg-transparent hover:text-primary"
                                    title="Edit spark"
                                >
                                    <Settings className="h-4 w-4" />
                                </Button>

                                {/* Delete Button */}
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleDeleteClick(spark)}
                                    disabled={isDeleting}
                                    className="shrink-0 h-8 w-8 p-0 hover:!bg-transparent hover:text-primary disabled:opacity-50 disabled:cursor-not-allowed"
                                    title="Delete spark"
                                >
                                    <Trash2 className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    ))}
                </div>
            )}

            {/* Spark Config Editor Modal */}
            <SparkConfigEditor
                spark={editingSpark}
                eventId={activeEvent._id}
                open={showEditor}
                onClose={handleCloseEditor}
                onSuccess={handleSaveSuccess}
                onError={handleError}
            />

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={modal?.type === 'deleteConfirm'} onOpenChange={(open) => !open && closeModal()}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete Spark Configuration</AlertDialogTitle>
                        <AlertDialogDescription>
                            {modal?.type === 'deleteConfirm' && (() => {
                                const spark = modal.spark as Spark;
                                const submissionCount = modal.submissionCount || 0;
                                const submissionText = submissionCount > 0 
                                    ? ` Additionally, this will permanently delete ${submissionCount} submission${submissionCount !== 1 ? 's' : ''} associated with this spark.`
                                    : '';
                                return `This will permanently delete the spark template "${spark.name}" with ${spark.fields.length} field${spark.fields.length !== 1 ? 's' : ''}.${submissionText} This action cannot be undone.`;
                            })()}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel 
                            disabled={isDeleting}
                        >
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleDeleteConfirm}
                            disabled={isDeleting}
                        >
                            {isDeleting ? 'Deleting...' : 'Delete'}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Delete Error Dialog */}
            <AlertDialog open={modal?.type === 'deleteError'} onOpenChange={(open) => !open && closeModal()}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Cannot Delete Spark Configuration</AlertDialogTitle>
                        <AlertDialogDescription>
                            The spark &quot;{typeof modal?.spark === 'string' ? modal.spark : modal?.spark?.name}&quot; cannot be deleted because it is currently being used by one or more sessions.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogAction onClick={closeModal}>
                            OK
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Edit Error Dialog */}
            <AlertDialog open={modal?.type === 'editError'} onOpenChange={(open) => !open && closeModal()}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Cannot Edit Spark Configuration</AlertDialogTitle>
                        <AlertDialogDescription>
                            The spark &quot;{typeof modal?.spark === 'string' ? modal.spark : modal?.spark?.name}&quot; cannot be edited because it has submissions associated with it.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogAction onClick={closeModal}>
                            OK
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Color Picker Dialog */}
            <Dialog open={!!showColorPicker} onOpenChange={(open) => !open && setShowColorPicker(null)}>
                <DialogContent className="sm:max-w-md rounded-none p-4 gap-3">
                    <DialogHeader className="space-y-1">
                        <DialogTitle>Choose Color</DialogTitle>
                        <DialogDescription>
                            Select a color for this spark configuration.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="py-2 flex justify-center items-center">
                        <div className="color-picker-container">
                            <ColorPicker
                                value={tempColor}
                                onChange={setTempColor}
                                hideOpacity
                                hideColorTypeBtns
                                hideControls
                            />
                        </div>
                    </div>
                    <DialogFooter className="gap-3">
                        <Button 
                            variant="outline"
                            onClick={() => setShowColorPicker(null)}
                        >
                            Cancel
                        </Button>
                        <Button 
                            onClick={() => {
                                const spark = sparks.find(s => s._id === showColorPicker);
                                if (spark) {
                                    handleColorChange(spark, tempColor);
                                    setShowColorPicker(null);
                                }
                            }}
                            className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-none"
                        >
                            Save
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
}

export default function SparksManagement() {
    return (
        <AdminErrorBoundary 
            fallbackTitle="Sparks Management Error"
            fallbackMessage="There was an error loading the sparks management interface."
        >
            <SparksManagementCore />
        </AdminErrorBoundary>
    );
}