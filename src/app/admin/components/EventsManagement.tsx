"use client";

import { useState } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { useSession } from 'next-auth/react';
import { debugError } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { StateCard } from '@/components/ui/state-card';
import AdminErrorBoundary from './AdminErrorBoundary';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface Event {
    _id: string;
    name: string;
    active: boolean;
    createdAt: number;
}

function EventsManagementCore() {
    const { data: sessionData } = useSession();
    const events = useQuery(api.events.getAllEvents, {
        username: sessionData?.user?.username || ''
    });
    const createEvent = useMutation(api.events.createEvent);
    const updateEvent = useMutation(api.events.updateEvent);
    const activateEvent = useMutation(api.events.activateEvent);
    const deactivateEvent = useMutation(api.events.deactivateEvent);
    const deleteEvent = useMutation(api.events.deleteEvent);

    const [eventName, setEventName] = useState('');
    const [error, setError] = useState<string | null>(null);
    const [editingEvent, setEditingEvent] = useState<Event | null>(null);
    const [editingName, setEditingName] = useState('');
    const [showDeleteConfirm, setShowDeleteConfirm] = useState<Event | null>(null);
    const [isDeleting, setIsDeleting] = useState(false);

    const sortedEvents = events || []; // Events are already sorted by the optimized getAllEvents function

    const handleAddEvent = async () => {
        if (!eventName.trim()) return;
        setError(null);

        try {
            await createEvent({
                username: sessionData?.user?.username || '',
                name: eventName.trim(),
                active: false
            });
            setEventName('');
            setError(null);
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to add event');
            debugError('Error adding event:', error);
        }
    };

    const startEditing = (event: Event) => {
        setEditingEvent(event);
        setEditingName(event.name);
    };

    const handleEditEvent = async (event: Event, newName: string) => {
        if (!newName.trim() || newName === event.name) return setEditingEvent(null);

        try {
            await updateEvent({
                username: sessionData?.user?.username || '',
                eventId: event._id as Id<"events">,
                name: newName.trim(),
            });
            setEditingEvent(null);
            setError(null);
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to update event');
            debugError('Error updating event:', error);
        }
    };

    const handleToggleActive = async (event: Event) => {
        try {
            if (!event.active) {
                await activateEvent({ 
                    username: sessionData?.user?.username || '',
                    eventId: event._id as Id<"events"> 
                });
            } else {
                await deactivateEvent({ 
                    username: sessionData?.user?.username || '',
                    eventId: event._id as Id<"events"> 
                });
            }
            setError(null);
        } catch (error) {
            setError(error instanceof Error ? error.message : `Failed to ${event.active ? 'deactivate' : 'activate'} event`);
            debugError('Error toggling event:', error);
        }
    };

    const handleKeyPress = (e: React.KeyboardEvent, event: Event) => {
        if (e.key === 'Enter') {
            handleEditEvent(event, editingName);
        } else if (e.key === 'Escape') {
            setEditingEvent(null);
        }
    };

    const handleDeleteEvent = async (event: Event) => {
        if (event.active) return;
        setShowDeleteConfirm(event);
    };

    const confirmDelete = async () => {
        if (!showDeleteConfirm) return;
        setIsDeleting(true);

        try {
            await deleteEvent({ 
                username: sessionData?.user?.username || '',
                eventId: showDeleteConfirm._id as Id<"events"> 
            });
            setError(null);
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to delete event');
            debugError('Error deleting event:', error);
        } finally {
            setIsDeleting(false);
            setShowDeleteConfirm(null);
        }
    };

    if (events === undefined) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard state="loading" title="Loading events..." />
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Add Event Form */}
            <div className="space-y-4">
                <Input
                    type="text"
                    placeholder="Enter event name"
                    value={eventName}
                    onChange={(e) => setEventName(e.target.value)}
                    onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                        if (e.key === 'Enter') {
                            handleAddEvent();
                        }
                    }}
                    className="input-standard"
                />
                <Button 
                    onClick={handleAddEvent}
                    className="w-full"
                >
                    Add Event
                </Button>
            </div>

            {/* Error Message */}
            {error && (
                <div className="flex justify-center items-center min-h-[200px]">
                    <StateCard 
                        state="error"
                        title="Error"
                        message={error}
                    />
                </div>
            )}
            
            {/* Empty State */}
            {events && sortedEvents.length === 0 && !error && (
                <div className="flex justify-center items-center min-h-[200px]">
                    <StateCard 
                        state="empty"
                        title="No Events Found"
                        message="Create your first event to get started."
                    />
                </div>
            )}

            {/* Events List */}
            {sortedEvents.length > 0 && (
                <div className="space-y-4">
                    {sortedEvents.map((event) => (
                        <div 
                            key={event._id}
                            className="flex items-center gap-4 p-4 border border-border rounded-md hover:bg-muted/50 transition-colors min-h-[3rem]"
                        >
                                    {/* Checkbox */}
                                    <Checkbox
                                        checked={event.active}
                                        onCheckedChange={() => handleToggleActive(event)}
                                        className="shrink-0"
                                    />

                                    {/* Event Name (Editable) */}
                                    <div className="flex-1 min-w-0">
                                        {editingEvent?._id === event._id ? (
                                            <Input
                                                type="text"
                                                value={editingName}
                                                onChange={(e) => setEditingName(e.target.value)}
                                                onKeyDown={(e) => handleKeyPress(e, event)}
                                                onBlur={() => handleEditEvent(event, editingName)}
                                                autoFocus
                                                className="input-standard"
                                            />
                                        ) : (
                                            <div
                                                className="text-foreground hover:text-primary transition-colors cursor-text truncate"
                                                onClick={() => startEditing(event)}
                                            >
                                                {event.name}
                                            </div>
                                        )}
                                    </div>

                                    {/* Delete Button */}
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleDeleteEvent(event)}
                                        disabled={event.active}
                                        className="shrink-0 h-8 w-8 p-0 hover:text-primary disabled:opacity-50"
                                        title={event.active ? 'Cannot delete active event' : 'Delete event'}
                                    >
                                        <Trash2 className="h-4 w-4" />
                                    </Button>
                                </div>
                            ))}
                </div>
            )}

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={!!showDeleteConfirm} onOpenChange={(open) => !open && setShowDeleteConfirm(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete Event</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete &quot;{showDeleteConfirm?.name}&quot;? This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel 
                            disabled={isDeleting}
                        >
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={confirmDelete}
                            disabled={isDeleting}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            {isDeleting ? 'Deleting...' : 'Delete'}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
}

export default function EventsManagement() {
    return (
        <AdminErrorBoundary 
            fallbackTitle="Events Management Error"
            fallbackMessage="There was an error loading the events management interface."
        >
            <EventsManagementCore />
        </AdminErrorBoundary>
    );
}