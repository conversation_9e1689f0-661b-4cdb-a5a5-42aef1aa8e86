"use client";

import { useState, useMemo, useCallback } from 'react';
import { debugError } from '@/lib/utils';
import { StateCard } from '@/components/ui/state-card';
import AdminErrorBoundary from './AdminErrorBoundary';
import { Id } from "@/../convex/_generated/dataModel";
import { useSessionOperations } from '@/app/admin/hooks/useSessionOperations';
import { useSessionModals } from '@/app/admin/hooks/useSessionModals';
import { sortSessionsByDate, groupSessionsByDay } from '@/app/admin/utils/sessionUtils';
import SessionActions from '@/app/admin/components/sessions/SessionActions';
import SessionList from '@/app/admin/components/sessions/SessionList';
import SessionForm from '@/app/admin/components/sessions/SessionForm';
import SessionConfirmationModals from '@/app/admin/components/sessions/SessionConfirmationModals';
import NoActiveEventMessage from './ideas/components/NoActiveEventMessage';

interface Session {
    _id: string;
    name: string;
    day?: number;
    eventId: string;
    active: boolean;
    type: string;
    sparkId?: string;
    createdAt: number;
}

function SessionsManagementCore() {
    
    // Custom hooks for operations and modals
    const {
        sessions,
        activeEvent,
        votingStatus,
        sparks,
        error,
        createSession,
        updateSession,
        deleteSession,
        toggleSessionActive,
        stopVoting,
        setError,
    } = useSessionOperations();
    
    const {
        modalState,
        openAddModal,
        closeAddModal,
        openSwitchConfirm,
        closeSwitchConfirm,
        openDeleteConfirm,
        closeDeleteConfirm,
        openTypeChangeError,
        closeTypeChangeError,
        openDayEditModal,
        closeDayEditModal,
        openSparkSelectionModal,
        closeSparkSelectionModal,
    } = useSessionModals();

    // Local state for editing and forms
    const [editingSession, setEditingSession] = useState<Session | null>(null);
    const [editingName, setEditingName] = useState('');
    const [switchingSession, setSwitchingSession] = useState(false);
    const [addingSession, setAddingSession] = useState(false);
    const [updatingType, setUpdatingType] = useState<string | null>(null);
    const [deletingSession, setDeletingSession] = useState(false);
    const [editingDay, setEditingDay] = useState<number>(1);
    const [updatingDay, setUpdatingDay] = useState(false);
    const [updatingSessionWithSpark, setUpdatingSessionWithSpark] = useState(false);
    
    // Add Session Modal State
    const [sessionName, setSessionName] = useState('');
    const [sessionType, setSessionType] = useState('Ideas');
    const [sessionDay, setSessionDay] = useState(1);
    const [selectedSparkId, setSelectedSparkId] = useState<string>('');
    const [sparkSelectionError, setSparkSelectionError] = useState<string | null>(null);
    
    // Spark creation state
    const [showSparkEditor, setShowSparkEditor] = useState(false);

    const votingStarted = votingStatus?.votingStarted || false;

    // Memoize expensive computations using regular sessions
    const sortedSessions = useMemo(() => 
        sessions ? sortSessionsByDate(sessions) : [],
        [sessions]
    );

    const groupedSessions = useMemo(() => 
        groupSessionsByDay(sortedSessions),
        [sortedSessions]
    );

    const handleAddSession = async () => {
        setAddingSession(true);
        
        const result = await createSession(sessionName, sessionType, sessionDay, selectedSparkId);
        
        if (result.success) {
            // Reset form
            setSessionName('');
            setSessionType('Ideas');
            setSessionDay(1);
            setSelectedSparkId('');
            closeAddModal();
        }
        
        setAddingSession(false);
    };

    const handleUpdateDay = useCallback(async () => {
        if (!modalState.showDayEditModal) return;
        if (editingDay < 1) {
            setError('Day must be a positive number');
            return;
        }

        setUpdatingDay(true);
        const result = await updateSession(modalState.showDayEditModal._id, { day: editingDay });
        
        if (result.success) {
            closeDayEditModal();
        }
        
        setUpdatingDay(false);
    }, [modalState.showDayEditModal, editingDay, updateSession, closeDayEditModal, setError]);

    const handleConfirmSparkSelection = useCallback(async () => {
        if (!modalState.showSparkSelectionModal) return;
        if (!selectedSparkId) {
            setSparkSelectionError('Please select a spark configuration');
            return;
        }

        setUpdatingSessionWithSpark(true);
        setSparkSelectionError(null);
        
        const result = await updateSession(modalState.showSparkSelectionModal._id, {
            type: 'Sparks',
            sparkId: selectedSparkId as Id<"sparks">
        });
        
        if (result.success) {
            setSelectedSparkId('');
            closeSparkSelectionModal();
        } else {
            setSparkSelectionError(result.error || 'Failed to update session with spark configuration');
        }
        
        setUpdatingSessionWithSpark(false);
    }, [modalState.showSparkSelectionModal, selectedSparkId, updateSession, closeSparkSelectionModal]);

    const handleCancelSparkSelection = useCallback(() => {
        setSelectedSparkId('');
        setSparkSelectionError(null);
        closeSparkSelectionModal();
    }, [closeSparkSelectionModal]);

    const startEditing = useCallback((session: Session) => {
        setEditingSession(session);
        setEditingName(session.name);
    }, []);

    const handleEditSession = useCallback(async (session: Session, newName: string) => {
        if (!newName.trim() || newName === session.name) return setEditingSession(null);

        const result = await updateSession(session._id, { name: newName.trim() });
        
        if (result.success) {
            setEditingSession(null);
        }
    }, [updateSession]);

    const handleUpdateSessionType = useCallback(async (session: Session, newType: string) => {
        if (newType === session.type) return;
        setUpdatingType(session._id);

        try {
            // If changing FROM Sparks to another type, we need to clear the sparkId
            if (session.type === 'Sparks' && newType !== 'Sparks') {
                const result = await updateSession(session._id, {
                    type: newType,
                    clearSparkId: true
                });
                
                if (!result.success && result.error?.includes("You can't change session type when there's ideas associated")) {
                    openTypeChangeError(result.error);
                }
            } 
            // If changing TO Sparks from another type, show spark selection modal
            else if (session.type !== 'Sparks' && newType === 'Sparks') {
                openSparkSelectionModal(session);
                // Don't update the session type yet - wait for spark selection
            } 
            else {
                // Normal type update
                const result = await updateSession(session._id, { type: newType });
                
                if (!result.success && result.error?.includes("You can't change session type when there's ideas associated")) {
                    openTypeChangeError(result.error);
                }
            }
        } catch (error) {
            debugError('Error updating session type:', error);
        } finally {
            setUpdatingType(null);
        }
    }, [updateSession, openSparkSelectionModal, openTypeChangeError]);

    // This function is now handled by the hook
    // const handleStopVoting = async () => { ... }

    // This function is now handled by the hook  
    // const toggleSessionActive = useCallback(async (session: Session) => { ... }

    const handleToggleActive = useCallback(async (session: Session) => {
        // If voting is started and trying to activate/deactivate session
        if (votingStarted) {
            openSwitchConfirm(session);
            return;
        }

        // TEMPORARY: No optimistic update - just call server
        try {
            await toggleSessionActive(session);
        } catch (error) {
            debugError('Error toggling session:', error);
        }
    }, [votingStarted, openSwitchConfirm, toggleSessionActive]);

    const handleConfirmSwitch = async () => {
        if (!modalState.showSwitchConfirm) return;

        setSwitchingSession(true);
        
        try {
            await stopVoting();
            await toggleSessionActive(modalState.showSwitchConfirm);
        } catch (error) {
            debugError('Error switching session:', error);
        } finally {
            setSwitchingSession(false);
            closeSwitchConfirm();
        }
    };

    const handleKeyPress = useCallback((e: React.KeyboardEvent, session: Session) => {
        if (e.key === 'Enter') {
            handleEditSession(session, editingName);
        } else if (e.key === 'Escape') {
            setEditingSession(null);
        }
    }, [editingName, handleEditSession]);

    const handleEditDay = useCallback((session: Session, currentDay: number) => {
        setEditingDay(currentDay);
        openDayEditModal(session);
    }, [openDayEditModal]);

    const handleDeleteSession = async (sessionId: string) => {
        setDeletingSession(true);
        
        await deleteSession(sessionId);
        
        setDeletingSession(false);
        closeDeleteConfirm();
    };

    const handleConfirmDelete = () => {
        if (!modalState.showDeleteConfirm) return;
        handleDeleteSession(modalState.showDeleteConfirm._id);
    };

    // Handle creating new spark
    const handleCreateNewSpark = () => {
        setShowSparkEditor(true);
    };

    // Handle spark creation success
    const handleSparkCreationSuccess = (sparkId: string) => {
        setShowSparkEditor(false);
        setSelectedSparkId(sparkId);
    };

    // Handle spark creation error
    const handleSparkCreationError = (error: string) => {
        console.error('Spark creation error:', error);
        // Keep the editor open so user can fix the error
    };

    // Handle spark editor close
    const handleSparkEditorClose = () => {
        setShowSparkEditor(false);
    };

    if (sessions === undefined || activeEvent === undefined || votingStatus === undefined) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard state="loading" title="Loading sessions..." />
            </div>
        );
    }

    if (!activeEvent) {
        return <NoActiveEventMessage />;
    }

    return (
        <div className="space-y-6">
            {/* Header with Add Button */}
            <SessionActions 
                onCreateSession={openAddModal}
            />

            {/* Sessions List */}
            <SessionList 
                groupedSessions={groupedSessions}
                sparks={sparks}
                editingSession={editingSession}
                editingName={editingName}
                updatingType={updatingType}
                onToggleActive={handleToggleActive}
                onStartEditing={startEditing}
                onEditSession={handleEditSession}
                onEditNameChange={setEditingName}
                onKeyPress={handleKeyPress}
                onUpdateSessionType={handleUpdateSessionType}
                onEditDay={handleEditDay}
                onDeleteSession={openDeleteConfirm}
                onConfigureSpark={openSparkSelectionModal}
                error={error}
            />

            {/* Add Session Form */}
            <SessionForm 
                isOpen={modalState.showAddModal}
                onClose={closeAddModal}
                onSubmit={handleAddSession}
                sessionName={sessionName}
                setSessionName={setSessionName}
                sessionType={sessionType}
                setSessionType={setSessionType}
                sessionDay={sessionDay}
                setSessionDay={setSessionDay}
                selectedSparkId={selectedSparkId}
                setSelectedSparkId={setSelectedSparkId}
                isSubmitting={addingSession}
                sparks={sparks}
                onCreateNewSpark={handleCreateNewSpark}
            />

            {/* All Confirmation Modals */}
            <SessionConfirmationModals
                modalState={modalState}
                sparks={sparks}
                activeEvent={activeEvent}
                switchingSession={switchingSession}
                handleConfirmSwitch={handleConfirmSwitch}
                closeSwitchConfirm={closeSwitchConfirm}
                deletingSession={deletingSession}
                handleConfirmDelete={handleConfirmDelete}
                closeDeleteConfirm={closeDeleteConfirm}
                closeTypeChangeError={closeTypeChangeError}
                editingDay={editingDay}
                setEditingDay={setEditingDay}
                updatingDay={updatingDay}
                handleUpdateDay={handleUpdateDay}
                closeDayEditModal={closeDayEditModal}
                selectedSparkId={selectedSparkId}
                setSelectedSparkId={setSelectedSparkId}
                sparkSelectionError={sparkSelectionError}
                updatingSessionWithSpark={updatingSessionWithSpark}
                handleConfirmSparkSelection={handleConfirmSparkSelection}
                handleCancelSparkSelection={handleCancelSparkSelection}
                showSparkEditor={showSparkEditor}
                handleCreateNewSpark={handleCreateNewSpark}
                handleSparkCreationSuccess={handleSparkCreationSuccess}
                handleSparkCreationError={handleSparkCreationError}
                handleSparkEditorClose={handleSparkEditorClose}
            />
        </div>
    );
}

export default function SessionsManagement() {
    return (
        <AdminErrorBoundary 
            fallbackTitle="Sessions Management Error"
            fallbackMessage="There was an error loading the sessions management interface."
        >
            <SessionsManagementCore />
        </AdminErrorBoundary>
    );
}